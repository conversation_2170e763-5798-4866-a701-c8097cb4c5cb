# Company Profile Completion Feature

This document describes the implementation of the company profile completion percentage feature for the Yes Jobs Dashboard, specifically for company/recruiter profiles.

## Overview

The company profile completion feature calculates and displays a percentage indicating how complete a company's profile is based on various profile fields and sections. This helps recruiters understand what information they need to provide to have a complete profile, which can improve their ability to attract candidates.

## Implementation

### Files Created/Modified

1. **`src/utils/companyProfileCompletion.ts`** - Core utility functions for calculating company profile completion
2. **`src/hooks/useCompanyProfileCompletion.ts`** - React hooks for company profile completion functionality
3. **`src/components/Sections/CompanyProfileCompletionTips.tsx`** - Component showing completion tips for companies
4. **`src/components/Sections/DashboardSidebar.tsx`** - Updated to show dynamic completion percentage for company dashboards
5. **`src/utils/__tests__/companyProfileCompletion.test.ts`** - Unit tests for the utility functions

### Core Functions

#### `calculateCompanyProfileCompletion(companyData: ICompany | undefined): number`

Calculates the company profile completion percentage (0-100) based on the following weighted criteria:

- **Company Profile Fields (35%)**: abn, companyName, profilePicture, foundedDate, companySize, websiteUrl, location
- **About Company Description (15%)**: Company description text
- **Company Video (10%)**: Company video upload
- **Social Networks (10%)**: At least one complete social network entry
- **Company Photos (10%)**: At least one company photo uploaded
- **Perks and Benefits (10%)**: At least one complete perk/benefit entry
- **Company Achievements (5%)**: At least one complete achievement entry
- **Email Notifications (2.5%)**: Notification settings configured
- **Job Preferences (2.5%)**: Job posting preferences configured

#### `getCompanyProfileCompletionDetails(companyData: ICompany | undefined)`

Returns detailed information including:
- Completion percentage
- Array of missing fields with user-friendly names

### React Hooks

#### `useCompanyProfileCompletion()`

Returns:
- `percentage`: Completion percentage (0-100)
- `missingFields`: Array of missing field names
- `isLoading`: Loading state
- `error`: Error state
- `companyData`: Raw company profile data

#### `useCompanyProfileCompletionPercentage()`

Returns only the completion percentage for simpler use cases.

### Components

#### `CompanyProfileCompletionTips`

An expandable component that shows:
- Encouragement message when profile is incomplete
- List of missing fields (up to 5 shown, with count of additional fields)
- Congratulations message when profile is 100% complete

## Usage

### In DashboardSidebar

The company profile completion percentage is automatically calculated and displayed for company dashboards:

```tsx
{CompanyDashboard && (
  <>
    <div className="bg-offWhite-100 py-3 px-2 mt-10">
      <p className="text-sm text-black-100 font-medium mb-3">Profile Completion</p>
      <p className="text-sm text-black-100 font-medium text-right mb-3">
        {companyProfileCompletionPercentage}%
      </p>
      <div className="w-full bg-orange-100 bg-opacity-50 rounded-full h-2.5 mb-4">
        <div 
          className="bg-orange-100 h-2.5 rounded-full transition-all duration-300 ease-in-out" 
          style={{ width: `${companyProfileCompletionPercentage}%` }}
        ></div>
      </div>
    </div>
    {companyProfileCompletionPercentage < 100 && <CompanyProfileCompletionTips />}
  </>
)}
```

### Using the Hooks

```tsx
import { useCompanyProfileCompletion } from '@/hooks/useCompanyProfileCompletion';

function MyComponent() {
  const { percentage, missingFields, isLoading } = useCompanyProfileCompletion();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <p>Company profile is {percentage}% complete</p>
      {missingFields.length > 0 && (
        <ul>
          {missingFields.map(field => (
            <li key={field}>{field}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
```

## Company Profile Schema Mapping

The feature maps the company profile schema fields as follows:

### Required Company Profile Fields
- `companyProfile.abn` → "ABN Number"
- `companyProfile.companyName` → "Company Name"
- `companyProfile.profilePicture` → "Company Logo"
- `companyProfile.foundedDate` → "Founded Date"
- `companyProfile.companySize` → "Company Size"
- `companyProfile.websiteUrl` → "Website URL"
- `companyProfile.location` → "Company Location"

### Other Profile Sections
- `aboutCompany.description` → "Company Description"
- `aboutCompany.companyVideo` → "Company Video"
- `socialNetworks[]` → "Social Networks"
- `companyPhotos[]` → "Company Photos"
- `perksAndBenefits[]` → "Perks and Benefits"
- `companyAchievements[]` → "Company Achievements"
- `emailNotifications` → "Email Notifications"
- `jobPreferences` → "Job Preferences"

## Features

1. **Real-time Updates**: Percentage updates automatically when company profile data changes
2. **Smooth Animations**: Progress bar animates when percentage changes
3. **User-friendly Messages**: Clear indication of what fields are missing
4. **Conditional Display**: Only shows for company dashboards
5. **Expandable Tips**: Users can expand to see detailed missing fields
6. **Completion Celebration**: Special message when profile reaches 100%

## Testing

Unit tests are provided in `src/utils/__tests__/companyProfileCompletion.test.ts` covering:
- Complete company profile scenarios
- Incomplete company profile scenarios
- Edge cases (undefined/null data)
- Percentage calculation accuracy
- Missing fields detection
- Specific field validation (ABN, location, etc.)

## API Integration

The feature integrates with the existing company profile API:
- **Hook**: `useGetCompanyProfile` from `@/hooks/useQuery`
- **Endpoint**: `/company-profile/` (via `API_ROUTES.PROFILE.COMPANY_PROFILE`)
- **Response Type**: `IGetCompanyProfileResponseDto`

## Comparison with Jobseeker Profile Completion

| Aspect | Jobseeker Profile | Company Profile |
|--------|------------------|-----------------|
| **Core Fields** | Personal info, skills, experience | Company info, description, media |
| **Weight Distribution** | More focus on experience/education | More focus on company branding |
| **Key Sections** | CV, portfolio, skills | Photos, videos, achievements |
| **Completion Criteria** | Individual career focus | Business/organizational focus |

## Future Enhancements

1. **Weighted Scoring**: Different fields could have different importance weights based on recruitment effectiveness
2. **Industry-specific Scoring**: Different completion criteria based on company industry
3. **Completion Rewards**: Gamification elements for completing profile sections
4. **Smart Suggestions**: AI-powered suggestions for improving company profile content
5. **Progress Tracking**: Historical tracking of completion progress over time
6. **Competitor Analysis**: Compare completion level with similar companies
7. **SEO Score**: Additional scoring based on profile SEO optimization

## Integration Points

The company profile completion feature integrates seamlessly with:
- Company dashboard sidebar
- Company settings pages
- Profile editing workflows
- Onboarding processes
- Analytics and reporting systems

This feature helps companies understand the importance of a complete profile in attracting quality candidates and provides clear guidance on what information to add for maximum impact.