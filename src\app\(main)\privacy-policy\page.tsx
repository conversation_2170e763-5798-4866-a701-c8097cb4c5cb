import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import PrivacyPolicyContent from "./PrivacyPolicyContent";
import type { Metadata } from "next";
export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - privacy policy",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};
export default function PrivacyPolicy() {
  return (
    <>
      <PrivacyPolicyContent />
      <CTASection />
      <MobileAppSection />
    </>
  );
}
