import Link from "next/link";
import JobList from "./JobList";
import JobNavi<PERSON> from "@/components/Sections/JobNavigation";

const navItems = [
  { href: "/company-dashboard/all-jobs", label: "All Jobs" },
  { href: "/company-dashboard/recently-posted", label: "Recently Posted" },
  { href: "/company-dashboard/saved-candidates", label: "Saved Candidate" },
];

export default function AllJobs() {
  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 mb-7">
        <div>
          <h2 className="text-blue-100 text-3xl font-bold mb-3">Your Posted Jobs</h2>
          <p className="text-gray-100">Your Company dashboard</p>
        </div>
        <div>
          <Link
            href={"/company-dashboard/post-job"}
            className="inline-flex justify-center items-center gap-x-2 rounded-full bg-blue-100 text-white px-10 py-3 text-lg font-bold"
          >
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="29"
                fill="none"
                viewBox="0 0 28 29"
              >
                <path
                  stroke="#fff"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M14 25c5.799 0 10.5-4.701 10.5-10.5S19.799 4 14 4 3.5 8.701 3.5 14.5 8.201 25 14 25M9 14.5h10M14 9.5v10"
                ></path>
              </svg>
            </span>
            <span>Post a Job</span>
          </Link>
        </div>
      </div>
      <div className="space-y-6">
        <JobNavigation navItems={navItems} />
        <JobList />
      </div>
    </>
  );
}
