"use client";

import { Camera } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useUpdateJobSeekerProfile, useUploadProfilePicture } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { IProfileData, IJobPreferences } from "@/types/query.types";

const TellUsAboutYourself = () => {
  const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";
  const router = useRouter();

  // Query hooks with loading states
  const { data, isLoading: isProfileLoading } = useGetJobSeekerProfile();
  const userProfile = data?.data.userProfile;
  const user = data?.data.user;

  // Mutation hooks with pending states
  const { mutateAsync: uploadProfilePicture } = useUploadProfilePicture();
  const [isUploadingProfilePicture, setIsUploadingProfilePicture] = useState(false);
  const { mutate: updateProfile, isPending: isUpdatingProfile } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      localStorage.setItem("profileCompletionStep", "educational-details");
      router.push("/profile-completion?stepId=educational-details");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const [profilePicture, setProfilePicture] = useState(DEFAULT_IMAGE);

  // Track if location was selected from suggestions
  const [isLocationSelected, setIsLocationSelected] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Places autocomplete
  const {
    ready,
    value,
    setValue: setLocationValue,
    suggestions: { status, data: suggestionsData },
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      componentRestrictions: { country: "au" },
    },
  });

  // Define a type for address components
  interface AddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
  }

  // Handle location selection
  const handleSelect = async (description: string) => {
    setLocationValue(description, false);
    clearSuggestions();

    try {
      const results = await getGeocode({ address: description });
      const { lat, lng } = getLatLng(results[0]);
      const address = results[0].formatted_address;

      // Extract address components
      const addressComponents = results[0].address_components;
      const city =
        addressComponents.find((c: AddressComponent) => c.types.includes("locality"))?.long_name ||
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_2")
        )?.long_name ||
        "";
      const state =
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_1")
        )?.long_name || "";
      const country =
        addressComponents.find((c: AddressComponent) => c.types.includes("country"))?.long_name ||
        "";

      // Update form values directly
      setValue("location", {
        type: "Point" as const, // Use const assertion to ensure type is exactly "Point"
        coordinates: [lng, lat] as [number, number],
        formattedAddress: address,
        city,
        state,
        country,
      });

      // Mark location as selected from suggestions
      setIsLocationSelected(true);
      setLocationError(null); // Clear error when valid location is selected
    } catch {
      // Use err instead of error to avoid unused variable warning
      toast.error("Failed to get location details");
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    reset,
  } = useForm<
    IProfileData["userProfile"] & { email: string; location: IJobPreferences["location"] }
  >({
    defaultValues: {
      email: "",
      dob: "",
      firstName: "",
      lastName: "",
      phoneNo: "",
      websiteUrl: "",
      portfolioUrl: "",
      shortBio: "",
      profilePicture: "",
      designation: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
        formattedAddress: "",
        city: "",
        state: "",
        country: "",
      },
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  // Initialize form with user data
  useEffect(() => {
    if (userProfile && user) {
      // Check if location data exists in the API response
      const locationData = userProfile.location
        ? {
            type: "Point" as const,
            coordinates: userProfile.location.coordinates || [0, 0],
            formattedAddress: userProfile.location.formattedAddress || "",
            city: userProfile.location.city || "",
            state: userProfile.location.state || "",
            country: userProfile.location.country || "",
          }
        : {
            type: "Point" as const,
            coordinates: [0, 0] as [number, number],
            formattedAddress: "",
            city: "",
            state: "",
            country: "",
          };

      // If there's location data, set the location value for autocomplete
      if (locationData.formattedAddress) {
        setLocationValue(locationData.formattedAddress, false);
        setIsLocationSelected(true); // Mark as selected if we have valid data from API
        setLocationError(null); // Clear any existing error
      } else if (locationData.city || locationData.country) {
        // Fallback to city and country if no formatted address
        const locationString = [locationData.city, locationData.country].filter(Boolean).join(", ");
        setLocationValue(locationString, false);
        setIsLocationSelected(true); // Mark as selected if we have valid data from API
        setLocationError(null); // Clear any existing error
      }

      // Create a new object with all user data and the location field
      const formData = {
        ...userProfile,
        email: user.email || "",
        dob: userProfile.dob ? new Date(userProfile.dob).toISOString().split("T")[0] : "",
        location: locationData,
      };

      // Form data is ready for reset
      reset(formData);
      setProfilePicture(userProfile.profilePicture || DEFAULT_IMAGE);
    }
  }, [userProfile, user, reset, setLocationValue]);

  const handleProfilePictureChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size should be less than 5MB");
      return;
    }

    if (!file.type.startsWith("image/")) {
      toast.error("Please upload a valid image file");
      return;
    }

    try {
      // Create a temporary URL for preview before upload completes
      const tempPreviewUrl = URL.createObjectURL(file);
      setProfilePicture(tempPreviewUrl);
      setIsUploadingProfilePicture(true);

      const response = await uploadProfilePicture(file);

      // Revoke the temporary URL to free up memory
      URL.revokeObjectURL(tempPreviewUrl);

      // Set the actual uploaded image URL
      setProfilePicture(response.data.profilePicture);
      setValue("profilePicture", response.data.profilePicture);
      toast.success("Profile picture uploaded successfully");
    } catch (error) {
      // Revert to previous profile picture on error
      setProfilePicture(userProfile?.profilePicture || DEFAULT_IMAGE);
      toast.error((error as Error).message || "Failed to upload profile picture");
    } finally {
      setIsUploadingProfilePicture(false);
    }
  };

  const onSubmit: SubmitHandler<
    IProfileData["userProfile"] & { location: IJobPreferences["location"] }
  > = async (formData) => {
    // Extract location data
    const { location, ...rest } = formData;

    // Additional validation for location - must be selected from suggestions
    if (
      !location?.formattedAddress ||
      !location?.coordinates ||
      location.coordinates[0] === 0 ||
      location.coordinates[1] === 0 ||
      !isLocationSelected
    ) {
      setLocationError("Please select valid location");
      return;
    }

    // Validate date of birth is not in the future
    if (formData.dob) {
      const selectedDate = new Date(formData.dob);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

      if (selectedDate > today) {
        toast.error("Date of birth cannot be in the future");
        return;
      }
    }

    updateProfile({
      userProfile: {
        ...rest,
        dob: formData.dob ? new Date(formData.dob).toISOString() : "",
        // Include location object directly
        location: {
          type: "Point",
          coordinates: location.coordinates || [0, 0],
          formattedAddress: location.formattedAddress || "",
          city: location.city || "",
          state: location.state || "",
          country: location.country || "",
        },
      },
    });
  };

  if (isProfileLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-blue-600 text-2xl font-bold">Tell us about yourself</h2>
      <h3 className="text-3xl font-semibold text-gray-800 mt-2 mb-6">My Profile</h3>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Profile Picture Upload */}
        <Label
          htmlFor="profile-picture"
          className="inline-flex items-center space-x-4 cursor-pointer relative"
        >
          <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden relative">
            {profilePicture ? (
              <Image
                src={profilePicture}
                alt="Profile Picture"
                width={96}
                height={96}
                className={`object-cover ${isUploadingProfilePicture ? "opacity-50" : ""}`}
              />
            ) : (
              <Camera className="w-8 h-8 text-gray-400" />
            )}
            {isUploadingProfilePicture && (
              <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-full">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-white"></div>
                <span className="absolute text-white text-xs mt-8">Uploading...</span>
              </div>
            )}
          </div>
          <div>
            <div className="text-gray-800 text-lg font-medium">Profile Picture</div>
            <Input
              id="profile-picture"
              type="file"
              accept="image/*"
              className="mt-1 hidden"
              onChange={handleProfilePictureChange}
              disabled={isUploadingProfilePicture}
            />
          </div>
        </Label>

        {/* Form Fields */}
        <div className="xl:w-[80%]">
          <div className="grid lg:grid-cols-2 gap-7">
            {/* First Name */}
            <div>
              <Label htmlFor="first-name" className="mb-2 block text-gray-700">
                First Name
              </Label>
              <Input
                className={inputClasses}
                id="first-name"
                {...register("firstName", {
                  required: "First name is required",
                })}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
              {errors.firstName && (
                <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <Label htmlFor="last-name" className="mb-2 block text-gray-700">
                Last Name
              </Label>
              <Input
                className={inputClasses}
                id="last-name"
                {...register("lastName", {
                  required: "Last name is required",
                })}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
              {errors.lastName && (
                <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="designation" className="mb-2 block text-gray-700">
                Designation
              </Label>
              <Input
                className={inputClasses}
                id="designation"
                {...register("designation", {
                  required: "designation is required",
                })}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
              {errors.designation && (
                <p className="text-red-500 text-sm mt-1">{errors.designation.message}</p>
              )}
            </div>
            {/* Email (readonly) */}
            <div>
              <Label htmlFor="email" className="mb-2 block text-gray-700">
                Email
              </Label>
              <Input
                className={`${inputClasses}  cursor-not-allowed`}
                id="email"
                type="email"
                readOnly
                value={user?.email}
                {...register("email")}
              />
            </div>

            {/* Phone Number */}
            <div>
              <Label htmlFor="phone" className="mb-2 block text-gray-700">
                Phone No
              </Label>
              <Input
                className={inputClasses}
                id="phone"
                type="tel"
                {...register("phoneNo", {
                  required: "Phone number is required",
                })}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
              {errors.phoneNo && (
                <p className="text-red-500 text-sm mt-1">{errors.phoneNo.message}</p>
              )}
            </div>

            {/* Date of Birth */}
            <div>
              <Label htmlFor="dob" className="mb-2 block text-gray-700">
                Date of Birth
              </Label>
              <Input
                className={`${inputClasses}`}
                id="dob"
                type="date"
                {...register("dob", {
                  required: "Date of birth is required",
                  validate: (value) => {
                    if (!value) return true;
                    const selectedDate = new Date(value);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return selectedDate <= today || "Date of birth cannot be in the future";
                  },
                })}
                max={new Date().toISOString().split("T")[0]} // Prevent selecting future dates
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
              {errors.dob && <p className="text-red-500 text-sm mt-1">{errors.dob.message}</p>}
            </div>

            {/* Address */}
            {/* Removed separate address field as it's redundant with the location field */}

            {/* Location */}
            <div>
              <Label htmlFor="location" className="mb-2 block text-gray-700">
                Location
              </Label>
              <div className="relative">
                <Input
                  className={inputClasses}
                  id="location"
                  placeholder="e.g., New York, USA"
                  value={value}
                  disabled={!ready || isUploadingProfilePicture || isUpdatingProfile}
                  required
                  {...register("location.formattedAddress", {
                    required: "Location is required",
                    onChange: (e) => {
                      setLocationValue(e.target.value);
                      setIsLocationSelected(false); // Reset when user types manually
                      setLocationError(null); // Clear error when user types
                      // Clear suggestions when user types
                      if (status === "OK") {
                        clearSuggestions();
                      }
                    },
                  })}
                />
                {status === "OK" && suggestionsData && suggestionsData.length > 0 && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-2 max-h-60 overflow-y-auto">
                    {suggestionsData.map(({ place_id, description }) => (
                      <li
                        key={place_id}
                        className="p-2 cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSelect(description)}
                      >
                        {description}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              {(locationError || errors.location?.message) && (
                <p className="text-red-500 text-sm mt-1">
                  {locationError || errors.location?.message}
                </p>
              )}
            </div>

            {/* Website URL */}
            <div>
              <Label htmlFor="websiteUrl" className="mb-2 block text-gray-700">
                Website URL (optional)
              </Label>
              <Input
                className={inputClasses}
                id="websiteUrl"
                type="url"
                {...register("websiteUrl")}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
            </div>

            {/* Portfolio URL */}
            <div>
              <Label htmlFor="portfolio" className="mb-2 block text-gray-700">
                Portfolio URL (optional)
              </Label>
              <Input
                className={inputClasses}
                id="portfolio"
                type="url"
                {...register("portfolioUrl")}
                disabled={isUploadingProfilePicture || isUpdatingProfile}
              />
            </div>
          </div>

          {/* Short Bio */}
          <div className="mt-7">
            <Label htmlFor="bio" className="mb-2 block text-gray-700">
              Short Bio (optional)
            </Label>
            <Textarea
              className="min-h-[120px] px-4 py-3 rounded-xl border border-gray-300"
              id="bio"
              {...register("shortBio")}
              disabled={isUploadingProfilePicture || isUpdatingProfile}
            />
          </div>

          {/* Submit Button */}
          <div className="flex flex-wrap gap-y-3 gap-x-5 mt-10">
            <button
              type="submit"
              disabled={!isValid || isUploadingProfilePicture || isUpdatingProfile}
              className={`font-bold py-3 px-8 rounded-full inline-flex items-center justify-center bg-orange-500 text-white transition-colors ${
                !isValid || isUploadingProfilePicture || isUpdatingProfile
                  ? "opacity-70 cursor-not-allowed"
                  : "hover:bg-orange-600"
              }`}
            >
              {isUpdatingProfile || isUploadingProfilePicture ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {isUploadingProfilePicture ? "Uploading Image..." : "Saving..."}
                </>
              ) : (
                "Next Step"
              )}
            </button>
            {isUploadingProfilePicture && (
              <p className="text-orange-500 text-sm ml-2 self-center">
                Please wait while your profile picture is being uploaded...
              </p>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default TellUsAboutYourself;
