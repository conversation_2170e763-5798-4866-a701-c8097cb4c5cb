"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { useMessageCountStore } from "@/store/useMessageCountStore";

/**
 * Hook to track when user is on the message page and update the store accordingly
 * This controls when to show/hide the message count badge
 */
export const useMessagePageVisibility = () => {
  const pathname = usePathname();
  const { setIsOnMessagePage } = useMessageCountStore();

  useEffect(() => {
    // Check if user is on the message page
    const isOnMessagePage = pathname === "/message";

    // Update the store
    setIsOnMessagePage(isOnMessagePage);

    // Debug log
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[MessagePageVisibility] User is ${isOnMessagePage ? "on" : "not on"} message page`
      );
    }
  }, [pathname, setIsOnMessagePage]);

  return pathname === "/message";
};
