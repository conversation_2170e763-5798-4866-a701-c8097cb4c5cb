"use client";
import React, { useState } from "react";
import { useProfileCompletion } from "@/hooks/useProfileCompletion";

const ProfileCompletionTips = () => {
  const { percentage, missingFields, isLoading } = useProfileCompletion();
  const [isExpanded, setIsExpanded] = useState(false);

  if (isLoading) {
    return (
      <div className="bg-offWhite-100 py-3 px-2 mt-4 animate-pulse">
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (percentage === 100) {
    return (
      <div className="bg-green-50 border border-green-200 py-3 px-2 mt-4 rounded-lg">
        <p className="text-sm text-green-800 font-medium text-center">
          🎉 Your profile is 100% complete!
        </p>
      </div>
    );
  }

  return (
    <div className="bg-offWhite-100 py-3 px-2 mt-4 rounded-lg">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between text-left"
      >
        <p className="text-sm text-black-100 font-medium">
          Complete your profile to get more job opportunities
        </p>
        {isExpanded ? (
          <svg
            className="h-4 w-4 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        ) : (
          <svg
            className="h-4 w-4 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {isExpanded && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <p className="text-xs text-gray-600 mb-2">Missing fields:</p>
          <ul className="space-y-1">
            {missingFields.slice(0, 5).map((field, index) => (
              <li key={index} className="text-xs text-orange-100 flex items-center">
                <span className="w-1.5 h-1.5 bg-orange-100 rounded-full mr-2"></span>
                {field}
              </li>
            ))}
            {missingFields.length > 5 && (
              <li className="text-xs text-gray-500">+{missingFields.length - 5} more fields</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProfileCompletionTips;
