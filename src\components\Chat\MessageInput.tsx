"use client";

import { Send, Paperclip } from "lucide-react";
import { useRef, useState, type FormEvent, type ChangeEvent, useCallback } from "react";
import { toast } from "sonner";
import FileUploadPreview from "./FileUploadPreview";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/useChatStore";
import { useRestriction } from "@/hooks/useRestriction";

interface MessageInputProps {
  onSendMessage: (content: string, files: File[]) => void;
  isLoading: boolean;
  disabled?: boolean;
  conversationId: string;
}

export default function MessageInput({
  onSendMessage,
  isLoading,
  disabled = false,
  conversationId,
}: MessageInputProps) {
  const [message, setMessage] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);
  const { setTyping } = useChatStore();
  const { canAccessMessages, isRestricted } = useRestriction();

  const handleFocus = useCallback(() => {
    setTyping(conversationId, true);
  }, [conversationId, setTyping]);

  const handleBlur = useCallback(() => {
    setTyping(conversationId, false);
  }, [conversationId, setTyping]);

  const handleSubmit = useCallback(
    (e: FormEvent) => {
      e.preventDefault();

      if (isRestricted || !canAccessMessages) {
        toast.error("Your account is restricted by admin. Contact admin for more details.");
        return;
      }

      if ((!message.trim() && selectedFiles.length === 0) || isLoading || disabled) {
        return;
      }

      onSendMessage(message.trim(), selectedFiles);
      setMessage("");
      setSelectedFiles([]);
      setTyping(conversationId, false);
    },
    [
      message,
      selectedFiles,
      isLoading,
      disabled,
      onSendMessage,
      conversationId,
      setTyping,
      isRestricted,
      canAccessMessages,
    ]
  );

  const handleFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFileChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setSelectedFiles((prev) => [...prev, ...filesArray]);
    }
  }, []);

  const removeSelectedFile = useCallback((index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  // Handle Enter key for sending message
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e);
      }
    },
    [handleSubmit]
  );

  return (
    <>
      <FileUploadPreview files={selectedFiles} onRemove={removeSelectedFile} />

      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          <button
            type="button"
            onClick={handleFileSelect}
            className={cn(
              "text-orange-100 hover:text-orange-200 transition-colors duration-200",
              (isLoading || disabled || isRestricted || !canAccessMessages) &&
                "opacity-50 cursor-not-allowed"
            )}
            disabled={isLoading || disabled || isRestricted || !canAccessMessages}
          >
            <Paperclip className="h-5 w-5" />
          </button>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            multiple
            disabled={isLoading || disabled || isRestricted || !canAccessMessages}
          />

          <div className="relative flex-1">
            <Input
              ref={messageInputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              placeholder={
                isRestricted || !canAccessMessages
                  ? "Account restricted - cannot send messages"
                  : "Type your message here..."
              }
              className={cn(
                "bg-gray-300 rounded-3xl h-12 pr-12 transition-all duration-200",
                "focus:ring-2 focus:ring-orange-100 focus:border-transparent",
                "hover:bg-gray-200",
                (isLoading || disabled || isRestricted || !canAccessMessages) &&
                  "opacity-50 cursor-not-allowed"
              )}
              disabled={isLoading || disabled || isRestricted || !canAccessMessages}
            />
            <Button
              type="submit"
              size="icon"
              className={cn(
                "text-orange-100 bg-transparent absolute top-1/2 right-2 transform -translate-y-1/2",
                "hover:bg-orange-50 hover:text-orange-200 transition-colors duration-200",
                (isLoading || disabled || isRestricted || !canAccessMessages) &&
                  "opacity-50 cursor-not-allowed"
              )}
              disabled={
                isLoading ||
                disabled ||
                isRestricted ||
                !canAccessMessages ||
                (!message.trim() && selectedFiles.length === 0)
              }
            >
              {isLoading ? <LoadingSpinner size="sm" /> : <Send className="h-4 w-4" />}
            </Button>
          </div>
        </form>
      </div>
    </>
  );
}
