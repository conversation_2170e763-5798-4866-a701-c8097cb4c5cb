"use client";

import { ChevronDown, Search, X } from "lucide-react";
import { useState, useEffect, useRef, useMemo } from "react";
import { useGetAllApplicants } from "@/hooks/useQuery";

interface SearchableApplicantCategorySelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function SearchableApplicantCategorySelect({
  value,
  onValueChange,
  placeholder = "Select category",
  className = "",
  disabled = false,
}: SearchableApplicantCategorySelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Fetch all applicants to extract categories
  const { data: applicantsData, isLoading } = useGetAllApplicants(
    { page: 1, limit: 1000 }, // Get a large number to capture all categories
    { enabled: isOpen }
  );

  // Extract unique categories from applicants
  const availableCategories = useMemo(() => {
    if (!applicantsData?.data?.applications) return [];

    const categories = new Set<string>();

    applicantsData.data.applications.forEach((application) => {
      if (application.job?.jobCategory) {
        categories.add(application.job.jobCategory);
      }
    });

    return Array.from(categories).sort();
  }, [applicantsData]);

  // Filter categories based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return availableCategories;

    return availableCategories.filter((category) =>
      category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [availableCategories, searchTerm]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleSelect = (category: string) => {
    onValueChange(category);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange("");
  };

  const displayValue = value || "";

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-[40px] px-4 rounded-full border border-gray-300 shadow
          flex items-center justify-between bg-white text-left
          ${disabled ? "opacity-50 cursor-not-allowed" : "hover:border-gray-400 cursor-pointer"}
          ${isOpen ? "border-gray-300 ring-1" : ""}
        `}
      >
        <span className={displayValue ? "text-gray-900" : "text-gray-500"}>
          {displayValue || placeholder}
        </span>
        <div className="flex items-center gap-2">
          {value && !disabled && (
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" onClick={handleClear} />
          )}
          <ChevronDown
            className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? "rotate-180" : ""}`}
          />
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search Input */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-100" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 text-gray-100 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
          </div>

          {/* Categories List */}
          <div className="max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">Loading categories...</div>
            ) : filteredCategories.length > 0 ? (
              filteredCategories.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => handleSelect(category)}
                  className={`
                    w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors
                    ${value === category ? "bg-orange-50 text-orange-600" : "text-gray-900"}
                  `}
                >
                  {category}
                </button>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? "No categories found" : "No categories available"}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
