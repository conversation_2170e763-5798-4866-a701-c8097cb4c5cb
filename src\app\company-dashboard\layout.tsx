import type { Metada<PERSON> } from "next";
import type React from "react"; // Import React
import SidebarLayout from "./SidebarLayout";
import Header from "@/components/layout/Header";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - company dashboard",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};

export default function CompanyDashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header dashboardPages={true} />
      <div className="container mx-auto py-14">
        <div className="lg:flex gap-x-10">
          <SidebarLayout />
          <div className="border border-gray-300 lg:w-[calc(100%-350px)] rounded-[18px] lg:p-7 p-2">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
