import { ICompany, Ilocation } from "@/types/query.types";

/**
 * Calculates the company profile completion percentage based on the company profile data
 * @param companyData - The company profile data
 * @returns The completion percentage (0-100)
 */
export function calculateCompanyProfileCompletion(companyData: ICompany | undefined): number {
  if (!companyData) return 0;

  let completedFields = 0;
  let totalFields = 0;

  // Company Profile fields (weight: 35%)
  const companyProfileFields = [
    "abn",
    "companyName",
    "profilePicture",
    "foundedDate",
    "companySize",
    "websiteUrl",
    "location",
  ];

  companyProfileFields.forEach((field) => {
    totalFields++;
    const value = companyData.companyProfile?.[field as keyof typeof companyData.companyProfile];
    if (value && value !== "" && value !== null && value !== undefined) {
      // Special check for location object
      if (field === "location" && typeof value === "object") {
        const location = value as Ilocation;
        if (location.formattedAddress && location.city && location.country) {
          completedFields++;
        }
      } else if (field === "abn" && typeof value === "number" && value > 0) {
        completedFields++;
      } else {
        completedFields++;
      }
    }
  });

  // About Company (weight: 15%)
  totalFields++;
  if (companyData.aboutCompany?.description && companyData.aboutCompany.description.trim() !== "") {
    completedFields++;
  }

  // Company Video (weight: 10%)
  totalFields++;
  if (companyData.aboutCompany?.companyVideo?.url) {
    completedFields++;
  }

  // Social Networks (weight: 10%)
  totalFields++;
  if (companyData.socialNetworks && companyData.socialNetworks.length > 0) {
    const hasCompleteSocialNetwork = companyData.socialNetworks.some(
      (social) => social.networkName && social.networkUrl
    );
    if (hasCompleteSocialNetwork) completedFields++;
  }

  // Company Photos (weight: 10%)
  totalFields++;
  if (companyData.companyPhotos && companyData.companyPhotos.length > 0) {
    completedFields++;
  }

  // Perks and Benefits (weight: 10%)
  totalFields++;
  if (companyData.perksAndBenefits && companyData.perksAndBenefits.length > 0) {
    const hasCompletePerks = companyData.perksAndBenefits.some(
      (perk) => perk.benefitName && perk.benefitDescription
    );
    if (hasCompletePerks) completedFields++;
  }

  // Company Achievements (weight: 5%)
  totalFields++;
  if (companyData.companyAchievements && companyData.companyAchievements.length > 0) {
    const hasCompleteAchievements = companyData.companyAchievements.some(
      (achievement) => achievement.title && achievement.eventOrInstitute && achievement.detail
    );
    if (hasCompleteAchievements) completedFields++;
  }

  // Email Notifications (weight: 2.5%)
  totalFields++;
  if (companyData.emailNotifications) {
    // Check if at least one notification setting is configured (not all default)
    const hasConfiguredNotifications =
      companyData.emailNotifications.newApplications !== undefined &&
      companyData.emailNotifications.applicationUpdates !== undefined &&
      companyData.emailNotifications.marketingEmails !== undefined;
    if (hasConfiguredNotifications) completedFields++;
  }

  // Job Preferences (weight: 2.5%)
  totalFields++;
  if (companyData.jobPreferences) {
    const hasConfiguredJobPrefs =
      companyData.jobPreferences.autoPublish !== undefined &&
      companyData.jobPreferences.defaultJobDuration > 0 &&
      companyData.jobPreferences.defaultApplicationDeadline > 0;
    if (hasConfiguredJobPrefs) completedFields++;
  }

  // Calculate percentage
  const percentage = Math.round((completedFields / totalFields) * 100);
  return Math.min(percentage, 100); // Ensure it doesn't exceed 100%
}

/**
 * Gets company profile completion details with missing fields
 * @param companyData - The company profile data
 * @returns Object with completion percentage and missing fields
 */
export function getCompanyProfileCompletionDetails(companyData: ICompany | undefined) {
  if (!companyData) {
    return {
      percentage: 0,
      missingFields: ["Complete company profile setup required"],
    };
  }

  const missingFields: string[] = [];

  // Check company profile fields
  if (!companyData.companyProfile?.abn || companyData.companyProfile.abn <= 0) {
    missingFields.push("ABN Number");
  }
  if (!companyData.companyProfile?.companyName) missingFields.push("Company Name");
  if (!companyData.companyProfile?.profilePicture) missingFields.push("Company Logo");
  if (!companyData.companyProfile?.foundedDate) missingFields.push("Founded Date");
  if (!companyData.companyProfile?.companySize) missingFields.push("Company Size");
  if (!companyData.companyProfile?.websiteUrl) missingFields.push("Website URL");

  // Check location
  const companyLocation = companyData.companyProfile?.location;
  if (!companyLocation?.formattedAddress || !companyLocation?.city || !companyLocation?.country) {
    missingFields.push("Company Location");
  }

  // Check about company
  if (
    !companyData.aboutCompany?.description ||
    companyData.aboutCompany.description.trim() === ""
  ) {
    missingFields.push("Company Description");
  }

  // Check company video
  if (!companyData.aboutCompany?.companyVideo?.url) {
    missingFields.push("Company Video");
  }

  // Check social networks
  if (!companyData.socialNetworks || companyData.socialNetworks.length === 0) {
    missingFields.push("Social Networks");
  } else {
    const hasCompleteSocialNetwork = companyData.socialNetworks.some(
      (social) => social.networkName && social.networkUrl
    );
    if (!hasCompleteSocialNetwork) {
      missingFields.push("Complete Social Network Details");
    }
  }

  // Check company photos
  if (!companyData.companyPhotos || companyData.companyPhotos.length === 0) {
    missingFields.push("Company Photos");
  }

  // Check perks and benefits
  if (!companyData.perksAndBenefits || companyData.perksAndBenefits.length === 0) {
    missingFields.push("Perks and Benefits");
  } else {
    const hasCompletePerks = companyData.perksAndBenefits.some(
      (perk) => perk.benefitName && perk.benefitDescription
    );
    if (!hasCompletePerks) {
      missingFields.push("Complete Perks and Benefits Details");
    }
  }

  // Check achievements
  if (!companyData.companyAchievements || companyData.companyAchievements.length === 0) {
    missingFields.push("Company Achievements");
  }

  return {
    percentage: calculateCompanyProfileCompletion(companyData),
    missingFields,
  };
}
