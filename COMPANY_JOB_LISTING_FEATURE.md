# Company Job Listing Feature

## Overview
This feature adds a dedicated page for viewing all jobs posted by a specific company, similar to the main job listing page but filtered to show only jobs from one company.

## Files Created/Modified

### New Files Created:
1. `src/app/(main)/company-job-listing/page.tsx` - Main page component wrapper
2. `src/app/(main)/company-job-listing/CompanyJobListingPage.tsx` - Main component for company job listings

### Files Modified:
1. `src/components/Cards/CandidateCard.tsx` - Added `jobsLink` prop and "View Jobs" button for companies
2. `src/app/(main)/all-companies/AllCompaniesListing.tsx` - Added navigation links to company job listings
3. `src/app/(main)/all-companies/[slug]/page.tsx` - Added navigation link to company job listings
4. `src/components/Cards/CompanyDetailCard.tsx` - Updated to use the jobs link

## Features Implemented

### ✅ Company Job Listing Page
- **URL**: `/company-job-listing?companyId={companyId}`
- **Hook Used**: `useGetCompanyJobs` from `useQuery.ts`
- **Layout**: Same as JobListingPage.tsx with left sidebar for job list and right panel for job details
- **Responsive**: Mobile modal for job details
- **No Filters**: As requested, no search or filter functionality
- **No Search**: As requested, no search bar

### ✅ Navigation Integration
- **All Companies Page**: Added "View Jobs" button on each company card
- **Company Detail Page**: Added "View Jobs" button and updated existing "View Jobs" link
- **Related Companies**: Added "View Jobs" button on related company cards

### ✅ Functionality
- **Job Selection**: Click on job cards to view details
- **Pagination**: Full pagination support
- **Results Per Page**: Configurable results per page
- **Apply Job**: Full apply job functionality
- **Save Job**: Save job functionality (though disabled for company-specific view)
- **Mobile Responsive**: Modal view for job details on mobile devices

## Usage

### Accessing Company Job Listings
1. **From All Companies Page**: Click "View Jobs" button on any company card
2. **From Company Detail Page**: Click "View Jobs" button or link
3. **Direct URL**: Navigate to `/company-job-listing?companyId={companyId}`

### URL Parameters
- `companyId` (required): The ID of the company whose jobs to display
- `page` (optional): Page number for pagination
- `limit` (optional): Number of jobs per page
- `jobId` (optional): ID of selected job for direct linking

## Technical Details

### Hook Integration
- Uses `useGetCompanyJobs(companyId, params)` hook
- Supports pagination with `page` and `limit` parameters
- Returns `IGetCompanyJobsResponseDto` with jobs array and pagination info

### Type Safety
- Uses `ICompanyJob` type for job data
- Implements `ICompanyJobSearchParams` interface for search parameters
- Full TypeScript support with proper type checking

### State Management
- URL-based state management for pagination and job selection
- Responsive design with mobile modal state
- Query client integration for cache invalidation

### Error Handling
- Loading states for data fetching
- Error states for failed requests
- Empty states when no jobs are found
- Validation for required companyId parameter

## Future Enhancements (Not Implemented)
- Search functionality within company jobs
- Filter options for company jobs
- Sorting options
- Job categories filtering
- Date range filtering

## Testing
The feature has been implemented and is ready for testing. The development server should be running and the pages should be accessible through the navigation links.