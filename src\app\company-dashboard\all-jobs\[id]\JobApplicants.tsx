"use client";

import { Search, SlidersHorizontal } from "lucide-react";
import { useState } from "react";
import CompanyDashboardTable from "@/components/Cards/CompanyDashboardTable";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useGetJobApplicants } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";

interface JobApplicantsProps {
  jobId: string;
}

export default function JobApplicants({ jobId }: JobApplicantsProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [status, setStatus] = useState<string | undefined>(undefined);
  const { data, isLoading, isError } = useGetJobApplicants(jobId, status, {
    refetchOnWindowFocus: true,
  });

  const applications = data?.data?.applications || [];

  // Filter applications based on search term
  const filteredApplications = applications.filter((application) => {
    const fullName =
      `${application.jobSeeker.userProfile.firstName} ${application.jobSeeker.userProfile.lastName}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase());
  });

  // Format data for the table
  const tableData = filteredApplications.map((application) => ({
    name: `${application.jobSeeker.userProfile.firstName} ${application.jobSeeker.userProfile.lastName}`,
    jobTitle: application.job.jobTitle,
    appliedDate: formatDate(application.appliedDate),
    imageUrl: application.jobSeeker.userProfile.profilePicture,
    id: application._id,
    jobSeekerId: application.jobSeeker._id,
    status: application.status,
    cvUrl: application.jobSeeker.cvAttachments?.cvUrl,
    designation: application.jobSeeker.userProfile.designation,
  }));

  return (
    <div>
      <div className="flex flex-wrap gap-3 justify-between sm:p-6">
        <div>
          <h2 className="text-black-100 text-2xl font-bold">All Applied Applicants</h2>
        </div>
        <div className="sm:flex gap-2 sm:ml-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" />
            <Input
              type="search"
              placeholder="Search"
              className="pl-10 bg-white border border-gray-100 text-base rounded-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="bg-white border border-gray-100 rounded-full gap-3"
              >
                <SlidersHorizontal className="h-4 w-4" />
                Filters
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Filter By</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={status === undefined}
                onCheckedChange={(checked) => (checked ? setStatus(undefined) : null)}
              >
                All
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={status === "PENDING"}
                onCheckedChange={(checked) =>
                  checked ? setStatus("PENDING") : setStatus(undefined)
                }
              >
                Pending
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={status === "SHORTLISTED"}
                onCheckedChange={(checked) =>
                  checked ? setStatus("SHORTLISTED") : setStatus(undefined)
                }
              >
                Shortlisted
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={status === "REJECTED"}
                onCheckedChange={(checked) =>
                  checked ? setStatus("REJECTED") : setStatus(undefined)
                }
              >
                Rejected
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      ) : isError ? (
        <div className="text-center py-8 text-red-500">
          Error loading applicants. Please try again later.
        </div>
      ) : tableData.length === 0 ? (
        <div className="text-center py-8">No applicants found for this job.</div>
      ) : (
        <CompanyDashboardTable data={tableData} />
      )}
    </div>
  );
}
