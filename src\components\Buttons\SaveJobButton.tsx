"use client";

import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useSaveJob, useUnsaveJob } from "@/hooks/useMutation";
import { useRestriction } from "@/hooks/useRestriction";

interface SaveJobButtonProps {
  jobId: string;
  initialSavedState?: boolean;
}

const SaveJobButton: React.FC<SaveJobButtonProps> = ({ jobId, initialSavedState = false }) => {
  const [isSaved, setIsSaved] = useState(initialSavedState);
  const { canSaveJob, isRestricted } = useRestriction();

  const { mutate: saveJob, isPending: isSaving } = useSaveJob({
    onSuccess: () => {
      setIsSaved(true);
    },
  });

  const { mutate: unsaveJob, isPending: isUnsaving } = useUnsaveJob({
    onSuccess: () => {
      setIsSaved(false);
    },
  });

  const isLoading = isSaving || isUnsaving;

  useEffect(() => {
    setIsSaved(initialSavedState);
  }, [initialSavedState]);

  const toggleSave = () => {
    if (isRestricted) {
      toast.error("Your account is restricted by admin. Contact admin for more details.");
      return;
    }

    if (!canSaveJob) {
      toast.error("You need to be a jobseeker to save jobs.");
      return;
    }

    if (isSaved) {
      unsaveJob(jobId);
    } else {
      saveJob(jobId);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={toggleSave}
            disabled={isLoading}
            className="focus:outline-none transition-transform duration-200 self-baseline"
            aria-label={isSaved ? "Unsave Job" : "Save Job"}
          >
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
                className={`transition-all duration-200 ${isLoading ? "opacity-50 animate-pulse" : ""}`}
              >
                {isSaved ? (
                  <path
                    fill="#ec761e"
                    d="M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5z"
                  ></path>
                ) : (
                  <path
                    fill="#262626"
                    d="M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                  ></path>
                )}
              </svg>
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isRestricted ? "Account Restricted" : isSaved ? "Unsave Job" : "Save Job"}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default SaveJobButton;
