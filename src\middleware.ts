import { NextRequest, NextResponse } from "next/server";

const ACCESS_TOKEN_KEY = "__yes_jobs_access_token__";
const USER_ROLE_KEY = "__yes_jobs_user_role__";

const PUBLIC_ROUTES = new Set([
  "/sign-up",
  "/login",
  "/forget-password",
  "/forget-password/reset-password",
  "/forget-password/forgot-password-email-sent",
  "/about-us",
  "/how-it-works",
  "/contact",
  "/for-candidates",
  "/for-recruiters",
  "/privacy-policy",
  "/terms-and-conditions",
  "/all-companies",
  "/",
  "/company-job-listing",
  // "/message",
]);

const JOBSEEKER_ROUTES = [
  "/dashboard",
  "/my-resume",
  "/settings",
  "/profile-completed",
  "/profile-completion",
  "/about-us",
  "/how-it-works",
  "/contact",
  "/all-companies",
  "/message",
  "/company-job-listing",
];

const RECRUITER_ROUTES = [
  "/company-dashboard",
  "/company-settings",
  "/company-profile-completed",
  "/company-profile-completion",
  "/message",
  "/featured-job-success",
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = request.cookies.get(ACCESS_TOKEN_KEY)?.value;
  const userRole = request.cookies.get(USER_ROLE_KEY)?.value;

  const hasToken = token && token !== "undefined";

  console.log("[Middleware] →", {
    pathname,
    hasToken,
    userRole,
    cookies: request.cookies.getAll().map((c) => `${c.name}=${c.value}`),
  });

  // Block access to login and sign-up if already authenticated
  if (hasToken && userRole && ["/login", "/sign-up"].includes(pathname)) {
    const redirectUrl =
      userRole === "JOBSEEKER" ? "/dashboard/applied-jobs" : "/company-dashboard/all-jobs";

    console.log(
      `[Middleware] Auth user trying to access ${pathname}. Redirecting to ${redirectUrl}`
    );

    return NextResponse.redirect(new URL(redirectUrl, request.url));
  }

  // Allow public routes
  if (PUBLIC_ROUTES.has(pathname)) {
    console.log("[Middleware] Public route allowed:", pathname);
    return NextResponse.next();
  }

  // Also allow routes that *start with* public route paths (for dynamic routes)
  const isPublicSubRoute = Array.from(PUBLIC_ROUTES).some(
    (route) => pathname.startsWith(route) && route !== "/"
  );

  if (isPublicSubRoute) {
    console.log("[Middleware] Public sub-route allowed:", pathname);
    return NextResponse.next();
  }

  // If not authenticated, block access
  if (!hasToken || !userRole) {
    console.warn("[Middleware] No valid token or role. Redirecting to login.");
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Route-role enforcement
  const isJobseeker = userRole === "JOBSEEKER";
  const isRecruiter = userRole === "RECRUITER";

  if (isJobseeker && !isRouteAllowed(pathname, JOBSEEKER_ROUTES)) {
    console.warn("[Middleware] Unauthorized access by Jobseeker:", pathname);
    return NextResponse.redirect(new URL("/dashboard/applied-jobs", request.url));
  }

  if (isRecruiter && !isRouteAllowed(pathname, RECRUITER_ROUTES)) {
    console.warn("[Middleware] Unauthorized access by Recruiter:", pathname);
    return NextResponse.redirect(new URL("/company-dashboard/all-jobs", request.url));
  }

  return NextResponse.next();
}

function isRouteAllowed(pathname: string, allowedRoutes: string[]): boolean {
  return (
    allowedRoutes.includes(pathname) || allowedRoutes.some((route) => pathname.startsWith(route))
  );
}

export const config = {
  matcher: [
    "/((?!api|_next/static/|_next/image|favicon.ico|assets|images|fonts|css|media|robots.txt|sitemap.xml|firebase-messaging-sw.js).*)",
  ],
};
