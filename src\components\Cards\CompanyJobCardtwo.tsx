import { StarIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

import PrimaryButton from "../Buttons/PrimaryButton";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useRestriction } from "@/hooks/useRestriction";
import { toast } from "sonner";

interface CompanyJobCardtwoProps {
  imageUrl?: string;
  jobTitle?: string;
  companyName?: string;
  category?: string;
  jobType?: string;
  cityName?: string;
  salaryRange?: string;
  jobClosed?: boolean;
  expireDate?: string;
  dateCreated?: string;
  totalApplicants?: number;
  shortlistedApplicants?: number;
  viewJobLink?: string;
  editJobLink?: string;
  handleDelete?: () => void;
  toggleJobStatus?: () => void;
  handleBoostJob?: (jobId: string) => void; // Updated to accept jobId
  isDetailPage?: boolean;
  isLoading?: boolean;
  isDeleted?: boolean;
  jobId: string; // Add jobId to props
  premiumExpireAt?: string; // Add premiumExpireAt to props
}

const CompanyJobCardtwo: React.FC<CompanyJobCardtwoProps> = ({
  imageUrl,
  jobTitle,
  companyName,
  category,
  jobType,
  cityName,
  salaryRange,
  expireDate,
  dateCreated,
  totalApplicants = 0,
  shortlistedApplicants = 0,
  jobClosed,
  viewJobLink,
  editJobLink,
  handleDelete,
  toggleJobStatus,
  handleBoostJob,
  isDetailPage = false,
  isLoading = false, // Default to false
  isDeleted = false, // Default to false
  jobId, // Destructure jobId
  premiumExpireAt, // Destructure premiumExpireAt
}) => {
  const isPremiumJob = premiumExpireAt ? new Date(premiumExpireAt) > new Date() : false;
  const { canDeleteJob, canEditJob, isRestricted } = useRestriction();

  const handleRestrictedDelete = () => {
    if (isRestricted) {
      toast.error("Your account is restricted by admin. Contact admin for more details.");
      return;
    }
    handleDelete?.();
  };

  if (isLoading) {
    // Skeleton loader for the entire component
    return (
      <div className="rounded-[18px] border border-gray-200 p-[30px] animate-pulse">
        <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      </div>
    );
  }
  return (
    <div
      className={`${
        !jobClosed ? "border-gray-200" : "border-red-200"
      } rounded-[18px] border p-[30px] ${isDeleted ? " cursor-not-allowed relative" : ""}`}
    >
      {isDeleted && (
        <div className="bg-red-500 rounded-[18px] z-10 bg-opacity-45 absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
          <div className="text-black text-2xl">This job post was deleted</div>
        </div>
      )}
      <div className="flex flex-wrap">
        <div className="mr-4">
          <Image
            src={imageUrl || DEFAULT_IMAGE}
            alt="Company Logo"
            width={80}
            height={80}
            className="w-[80px] h-[80px] rounded-full"
          />
        </div>
        <div>
          <h2 className="text-2xl font-semibold text-black-100 mb-3">{jobTitle}</h2>
          <p className="text-black-100">
            <span className="text-gray-100">by</span> {companyName}{" "}
            <span className="text-gray-100">in</span> {category}
          </p>
        </div>

        <div className="ml-auto flex items-center">
          <label className="relative inline-flex items-center cursor-pointer me-3">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={!jobClosed}
              onChange={toggleJobStatus}
            />
            <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:bg-green-500 peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
            <span className="ml-3 text-sm font-medium text-gray-900">
              {jobClosed ? "Inactive" : "Active"}
            </span>
          </label>
          {!jobClosed && (
            <div>
              {isPremiumJob ? (
                <div className="flex items-center gap-x-2 bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                  <span>PREMIUM JOB</span>
                  <span>
                    <StarIcon />{" "}
                  </span>
                </div>
              ) : (
                <button
                  onClick={() => handleBoostJob?.(jobId)} // Pass jobId to handleBoostJob
                  className="flex items-center gap-x-2 bg-offWhite-100 text-orange-100 px-4 py-2 rounded-full text-sm font-medium"
                >
                  <span>Hire Faster</span>
                  <span>
                    <StarIcon />{" "}
                  </span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>
      <div className="flex gap-4 flex-wrap mt-6">
        <div className="bg-offWhite-100 text-orange-100 px-6 py-3 rounded-full">{jobType}</div>
        {cityName && (
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {cityName}
          </div>
        )}
        <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
          {salaryRange}
        </div>

        {jobClosed && (
          <div className="ml-auto">
            <Link
              href={"#"}
              className="flex items-center gap-x-2 bg-gray-300 text-gray-100 px-7 py-3 rounded-full text-lg font-medium"
            >
              <span>Job Closed</span>
            </Link>
          </div>
        )}
        {!isDetailPage && (
          <>
            <div className="ml-auto">
              <button
                onClick={handleRestrictedDelete}
                disabled={!canDeleteJob}
                className={`flex items-center gap-x-2 px-7 py-3 rounded-full text-lg font-medium ${
                  canDeleteJob
                    ? "bg-red-600 text-white hover:bg-red-700"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                <span>Delete</span>
              </button>
            </div>

            <div>
              <PrimaryButton
                text="View"
                link={viewJobLink || "#"}
                icon={
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill="#fff"
                      d="M16.522 2.76 6.346.961a1.56 1.56 0 0 0-1.81 1.268L2.21 15.433a1.563 1.563 0 0 0 1.267 1.81l10.176 1.796q.136.024.275.024a1.56 1.56 0 0 0 1.54-1.293l2.324-13.203a1.564 1.564 0 0 0-1.27-1.808m-2.85 14.377-9.56-1.688L6.328 2.862l9.56 1.689zM7.18 5.234a.94.94 0 0 1 1.086-.76l5.25.927a.937.937 0 0 1-.156 1.864 1 1 0 0 1-.165-.014l-5.255-.93a.937.937 0 0 1-.76-1.087m-.54 3.078a.94.94 0 0 1 1.088-.76l5.251.928a.938.938 0 0 1-.33 1.846l-5.251-.928a.94.94 0 0 1-.757-1.086m-.546 3.077a.94.94 0 0 1 1.086-.76l2.628.465a.937.937 0 1 1-.326 1.846l-2.626-.464a.936.936 0 0 1-.762-1.087"
                    ></path>
                  </svg>
                }
              />
            </div>
          </>
        )}

        <div className={`${isDetailPage ? "ml-auto" : ""}`}>
          {canEditJob ? (
            <PrimaryButton
              text="Edit"
              link={editJobLink || "#"}
              icon={
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill="#fff"
                    d="M16.522 2.76 6.346.961a1.56 1.56 0 0 0-1.81 1.268L2.21 15.433a1.563 1.563 0 0 0 1.267 1.81l10.176 1.796q.136.024.275.024a1.56 1.56 0 0 0 1.54-1.293l2.324-13.203a1.564 1.564 0 0 0-1.27-1.808m-2.85 14.377-9.56-1.688L6.328 2.862l9.56 1.689zM7.18 5.234a.94.94 0 0 1 1.086-.76l5.25.927a.937.937 0 0 1-.156 1.864 1 1 0 0 1-.165-.014l-5.255-.93a.937.937 0 0 1-.76-1.087m-.54 3.078a.94.94 0 0 1 1.088-.76l5.251.928a.938.938 0 0 1-.33 1.846l-5.251-.928a.94.94 0 0 1-.757-1.086m-.546 3.077a.94.94 0 0 1 1.086-.76l2.628.465a.937.937 0 1 1-.326 1.846l-2.626-.464a.936.936 0 0 1-.762-1.087"
                  ></path>
                </svg>
              }
            />
          ) : (
            <button
              disabled
              className="flex items-center gap-x-2 bg-gray-300 text-gray-500 px-7 py-3 rounded-full text-lg font-medium cursor-not-allowed"
              title="Account Restricted"
            >
              <span>Edit</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="none"
                viewBox="0 0 20 20"
              >
                <path
                  fill="currentColor"
                  d="M16.522 2.76 6.346.961a1.56 1.56 0 0 0-1.81 1.268L2.21 15.433a1.563 1.563 0 0 0 1.267 1.81l10.176 1.796q.136.024.275.024a1.56 1.56 0 0 0 1.54-1.293l2.324-13.203a1.564 1.564 0 0 0-1.27-1.808m-2.85 14.377-9.56-1.688L6.328 2.862l9.56 1.689zM7.18 5.234a.94.94 0 0 1 1.086-.76l5.25.927a.937.937 0 0 1-.156 1.864 1 1 0 0 1-.165-.014l-5.255-.93a.937.937 0 0 1-.76-1.087m-.54 3.078a.94.94 0 0 1 1.088-.76l5.251.928a.938.938 0 0 1-.33 1.846l-5.251-.928a.94.94 0 0 1-.757-1.086m-.546 3.077a.94.94 0 0 1 1.086-.76l2.628.465a.937.937 0 1 1-.326 1.846l-2.626-.464a.936.936 0 0 1-.762-1.087"
                ></path>
              </svg>
            </button>
          )}
        </div>
      </div>
      <div className="sm:flex flex-wrap gap-x-6 gap-y-3 mt-6">
        <div className="flex justify-between">
          <div className="text-blue-100">
            Date Created: <span className="text-black-100">{dateCreated}</span>{" "}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="text-orange-100">
            Expire Date: <span className="text-black-100">{expireDate}</span>{" "}
          </div>
        </div>
        <div className="flex justify-between sm:ml-auto">
          <div className="text-gray-100">
            Total Applicants:{" "}
            <span className="text-black-100 font-bold">{totalApplicants}</span>{" "}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="text-gray-100">
            Shortlisted Applicants:{" "}
            <span className="text-black-100 font-bold">{shortlistedApplicants}</span>{" "}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyJobCardtwo;
