import { useMemo } from "react";
import { useGetJobSeekerProfile } from "./useQuery";
import { calculateProfileCompletion, getProfileCompletionDetails } from "@/utils/profileCompletion";

/**
 * Custom hook to get profile completion percentage and details
 * @returns Object with completion percentage, missing fields, and loading state
 */
export function useProfileCompletion() {
  const { data: jobSeekerProfileData, isLoading, error } = useGetJobSeekerProfile();

  const completionData = useMemo(() => {
    if (!jobSeekerProfileData?.data) {
      return {
        percentage: 0,
        missingFields: ["Complete profile setup required"],
      };
    }

    return getProfileCompletionDetails(jobSeekerProfileData.data);
  }, [jobSeekerProfileData]);

  return {
    percentage: completionData.percentage,
    missingFields: completionData.missingFields,
    isLoading,
    error,
    profileData: jobSeekerProfileData?.data,
  };
}

/**
 * Custom hook to get only the profile completion percentage
 * @returns The completion percentage (0-100)
 */
export function useProfileCompletionPercentage() {
  const { data: jobSeekerProfileData } = useGetJobSeekerProfile();

  return useMemo(() => {
    return calculateProfileCompletion(jobSeekerProfileData?.data);
  }, [jobSeekerProfileData]);
}
