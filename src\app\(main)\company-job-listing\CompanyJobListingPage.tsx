"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useCallback, useMemo, Suspense } from "react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Pagination } from "@/components/JobSearch/Pagination";
import { ResultsPerPage } from "@/components/JobSearch/ResultsPerPage";
import JobDetail from "@/components/Cards/JobDetail";
import JobShortCard from "@/components/Cards/JobShortCard";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetCompanyJobs } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";
import type { ICompanyJob } from "@/types/query.types";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface ICompanyJobSearchParams {
  page?: number;
  limit?: number;
  jobId?: string;
}

export default function CompanyJobListingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Get company ID from URL parameters
  const companyId = searchParams.get("companyId") || "";

  // Initialize state from URL parameters
  const [selectedJobId, setSelectedJobId] = useState<string | null>(searchParams.get("jobId"));

  // Add modal state for mobile view
  const [isJobDetailModalOpen, setIsJobDetailModalOpen] = useState(false);

  const [searchState, setSearchState] = useState<ICompanyJobSearchParams>(() => {
    return {
      page: Number.parseInt(searchParams.get("page") || "1"),
      limit: Number.parseInt(searchParams.get("limit") || "10"),
    };
  });

  const { data, isLoading, isError } = useGetCompanyJobs(companyId, searchState);

  // Use useMemo to avoid recreating the jobs array on every render
  const jobs = useMemo(() => data?.data.jobs || [], [data?.data.jobs]);
  const pagination = data?.data.pagination;

  // Use useCallback for updateUrl function to avoid recreating it on every render
  const updateUrl = useCallback(
    (params: ICompanyJobSearchParams & { jobId?: string }) => {
      const urlParams = new URLSearchParams();
      const { jobId, ...searchParamsData } = params;

      // Always include companyId
      urlParams.set("companyId", companyId);

      Object.entries(searchParamsData).forEach(([key, value]) => {
        if (value !== undefined && value !== null && String(value) !== "") {
          urlParams.set(key, String(value));
        }
      });

      // Add jobId to URL params but don't include it in the search state
      if (jobId) {
        urlParams.set("jobId", jobId);
      }

      const url = urlParams.toString() ? `?${urlParams.toString()}` : `?companyId=${companyId}`;
      router.push(`/company-job-listing${url}`, { scroll: false });
    },
    [router, companyId]
  );

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newParams = {
      ...searchState,
      page,
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle limit change
  const handleLimitChange = (limit: number) => {
    const newParams = {
      ...searchState,
      limit,
      page: 1, // Reset to first page when changing limit
    };
    setSearchState(newParams);
    updateUrl(newParams);
  };

  // Handle job selection
  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId);
    // Only update URL with jobId, don't include it in searchState
    updateUrl({ ...searchState, jobId });

    // Open modal on mobile screens
    if (typeof window !== "undefined" && window.innerWidth < 1024) {
      // lg breakpoint
      setIsJobDetailModalOpen(true);
    }
  };

  // Set the first job as selected when jobs are loaded
  useEffect(() => {
    if (jobs.length > 0 && !selectedJobId && jobs[0]?._id) {
      setSelectedJobId(jobs[0]._id);
      // Only update URL with jobId, don't include it in searchState
      updateUrl({ ...searchState, jobId: jobs[0]._id });
    }
  }, [jobs, selectedJobId, searchState, updateUrl]);

  const selectedJob = jobs.find((job) => job._id === selectedJobId);

  // Format salary range for display
  const formatSalaryRange = (job: ICompanyJob) => {
    const currency = "$";
    const start = job.salaryRangeStart.toLocaleString();
    const end = job.salaryRangeEnd.toLocaleString();

    if (job.salaryType === "ANNUAL") {
      return `${currency}${start} - ${currency}${end} per year`;
    } else if (job.salaryType === "MONTHLY") {
      return `${currency}${start} - ${currency}${end} per month`;
    } else if (job.salaryType === "HOURLY") {
      return `${currency}${start} - ${currency}${end} per hour`;
    } else {
      return `${currency}${start} - ${currency}${end}`;
    }
  };

  // Parse responsibilities and skills from string to array
  const parseTextToArray = (text: string): string[] => {
    if (!text) return [];

    // Split by semicolons, commas, or newlines
    const items = text.split(/[;\n]/).filter((item) => item.trim().length > 0);

    if (items.length === 1 && items[0] && !items[0].includes(";") && !items[0].includes("\n")) {
      // If only one item and no delimiters, try splitting by periods or commas
      return text
        .split(/[.,]/)
        .filter((item) => item.trim().length > 0)
        .map((item) => item.trim());
    }

    return items.map((item) => item.trim());
  };

  if (!companyId) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Company ID is required to view jobs.</p>
          <Link href="/all-companies">
            <Button>Browse All Companies</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-[400px]">Loading jobs...</div>;
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        Error loading jobs. Please try again.
      </div>
    );
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <section className="bg-offWhite-100 py-20">
        <div className="container mx-auto">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold mb-6">Company Job Listings</h1>
              <Link href="/all-companies">
                <Button variant="outline">Browse All Companies</Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="lg:flex h-full">
            {/* Left side - Job listings */}
            <div className="w-full lg:w-[35%] space-y-5 h-full">
              {/* Results per page shown before job listings */}
              {pagination && (
                <ResultsPerPage pagination={pagination} onLimitChange={handleLimitChange} />
              )}
              {jobs.length === 0 ? (
                <div className="p-8 text-center border rounded-lg">
                  <p className="text-gray-500">No jobs found for this company.</p>
                  <p className="mt-2">This company may not have any active job postings.</p>
                </div>
              ) : (
                <div className="space-y-6 p-1">
                  {jobs.map((job) => (
                    <div
                      key={job._id}
                      onClick={() => handleJobSelect(job._id)}
                      className={`cursor-pointer transition-all ${selectedJobId === job._id ? "ring-2 ring-orange-100 rounded-[18px]" : ""}`}
                    >
                      <JobShortCard
                        _jobId={job._id}
                        imageUrl={
                          job.recruiterProfile.companyProfile.profilePicture || DEFAULT_IMAGE
                        }
                        jobTitle={job.jobTitle}
                        companyName={job.recruiterProfile.companyProfile.companyName}
                        category={job.jobCategory.replace(/_/g, " ")}
                        jobType={job.jobType.replace(/_/g, " ")}
                        cityName={job.location.city}
                        salaryRange={formatSalaryRange(job)}
                        deadline={formatDate(job.applicationDeadline)}
                        isSaved={false} // Company jobs don't have saved state in this context
                        premiumExpireAt={job.premiumExpireAt}
                        isDeadlinePassed={new Date() > new Date(job.applicationDeadline)}
                      />
                    </div>
                  ))}
                </div>
              )}
              {/* Pagination controls shown after job listings */}
              {pagination && jobs.length > 0 && (
                <Pagination
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onLimitChange={handleLimitChange}
                />
              )}
            </div>

            {/* Right side - Job details */}
            <div className="lg:w-[67%] xl:ml-10 ml-5 hidden lg:block sticky top-24 h-full">
              {selectedJob ? (
                <JobDetail
                  jobId={selectedJob._id}
                  imageUrl={
                    selectedJob.recruiterProfile.companyProfile.profilePicture || DEFAULT_IMAGE
                  }
                  jobTitle={selectedJob.jobTitle}
                  companyName={selectedJob.recruiterProfile.companyProfile.companyName}
                  category={selectedJob.jobCategory.replace(/_/g, " ")}
                  jobType={selectedJob.jobType.replace(/_/g, " ")}
                  cityName={selectedJob.location.city}
                  salaryRange={formatSalaryRange(selectedJob)}
                  deadline={formatDate(selectedJob.applicationDeadline)}
                  datePosted={formatDate(selectedJob.createdAt)}
                  location={selectedJob.location.formattedAddress}
                  experience={selectedJob.experienceLevel.replace(/_/g, " ")}
                  qualification={selectedJob.qualification.replace(/_/g, " ")}
                  careerLevel={selectedJob.careerLevel.replace(/_/g, " ")}
                  jobDescription={selectedJob.jobDescription}
                  keyResponsibilities={parseTextToArray(selectedJob.keyResponsibilities)}
                  skillsExperience={parseTextToArray(selectedJob.skillsAndExperience)}
                  skillsTags={selectedJob.skillsTag || []}
                  isSaved={false} // Company jobs don't have saved state in this context
                  premiumExpireAt={selectedJob.premiumExpireAt}
                  onApplySuccess={() => {
                    // Refresh the job list after successful application
                    queryClient.invalidateQueries({ queryKey: ["get-company-jobs"] });
                  }}
                  alreadyApplied={false} // This would need to be determined from the API
                  isJobDetailWeb={true}
                  isDeadlinePassed={new Date() > new Date(selectedJob?.applicationDeadline)}
                />
              ) : (
                <div className="p-8 text-center border rounded-lg">
                  <p className="text-gray-500">Select a job to view details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Job Detail Modal */}
      <Dialog open={isJobDetailModalOpen} onOpenChange={setIsJobDetailModalOpen}>
        <DialogContent className="w-full max-w-4xl max-h-[90vh] overflow-y-auto p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">Job Details</DialogTitle>
          </DialogHeader>
          <div className="p-6 pt-0">
            {selectedJob ? (
              <JobDetail
                jobId={selectedJob._id}
                imageUrl={
                  selectedJob.recruiterProfile.companyProfile.profilePicture || DEFAULT_IMAGE
                }
                jobTitle={selectedJob.jobTitle}
                companyName={selectedJob.recruiterProfile.companyProfile.companyName}
                category={selectedJob.jobCategory.replace(/_/g, " ")}
                jobType={selectedJob.jobType.replace(/_/g, " ")}
                cityName={selectedJob.location.city}
                salaryRange={formatSalaryRange(selectedJob)}
                deadline={formatDate(selectedJob.applicationDeadline)}
                datePosted={formatDate(selectedJob.createdAt)}
                location={selectedJob.location.formattedAddress}
                experience={selectedJob.experienceLevel.replace(/_/g, " ")}
                qualification={selectedJob.qualification.replace(/_/g, " ")}
                careerLevel={selectedJob.careerLevel.replace(/_/g, " ")}
                jobDescription={selectedJob.jobDescription}
                keyResponsibilities={parseTextToArray(selectedJob.keyResponsibilities)}
                skillsExperience={parseTextToArray(selectedJob.skillsAndExperience)}
                skillsTags={selectedJob.skillsTag || []}
                isSaved={false} // Company jobs don't have saved state in this context
                premiumExpireAt={selectedJob.premiumExpireAt}
                onApplySuccess={() => {
                  // Refresh the job list after successful application
                  queryClient.invalidateQueries({ queryKey: ["get-company-jobs"] });
                  setIsJobDetailModalOpen(false); // Close modal after applying
                }}
                alreadyApplied={false} // This would need to be determined from the API
                isJobDetailWeb={false} // Set to false for modal view
                isDeadlinePassed={new Date() > new Date(selectedJob?.applicationDeadline)}
              />
            ) : (
              <div className="p-8 text-center">
                <p className="text-gray-500">No job selected</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Suspense>
  );
}
