"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import ChatLayout from "@/components/Chat/ChatLayout";
import { useRestriction } from "@/hooks/useRestriction";
import RestrictedAccess from "@/components/RestrictedAccess";

export default function MessagePageContent({ params }: { params: { id?: string } }) {
  // Get the conversation ID from the URL search params
  const searchParams = useSearchParams();
  const [conversationId, setConversationId] = useState<string>("");
  const { canAccessMessages } = useRestriction();

  // Use useEffect to handle the URL params on the client side only
  useEffect(() => {
    const id = searchParams.get("id") || params.id || "";
    setConversationId(id);
  }, [searchParams, params.id]);

  // Show restriction message if user cannot access messages
  if (!canAccessMessages) {
    return <RestrictedAccess />;
  }

  return <ChatLayout conversationId={conversationId} />;
}
