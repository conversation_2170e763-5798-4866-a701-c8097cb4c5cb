"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const navItems = [
  { id: 1, href: "/settings/my-profile", label: "My Profile" },
  { id: 2, href: "/settings/cv-attachment", label: "CV Attachment" },
  { id: 3, href: "/settings/billing-payments", label: "Billing & Payments" },
  { id: 4, href: "/settings/password-security", label: "Password & Security" },
  { id: 5, href: "/settings/notification-settings", label: "Notification Settings" },
];
const navItems2 = [
  { id: 1, href: "/my-resume/academic-experience", label: "Academic Experience" },
  { id: 2, href: "/my-resume/educational-detail", label: "Educational Details" },
  { id: 3, href: "/my-resume/professional-experience", label: "Professional Experience" },
  { id: 4, href: "/my-resume/professional-skills", label: "Professional Skills" },
  { id: 5, href: "/my-resume/achievements", label: "Achievements" },
  { id: 6, href: "/my-resume/portfolio", label: "Portfolio" },
  { id: 7, href: "/my-resume/social-network", label: "Social Network" },
];

const SettingNavigation = ({
  MyResumePages = false,
  onNavigationClick,
}: {
  MyResumePages?: boolean;
  onNavigationClick?: () => void;
}) => {
  const pathname = usePathname();

  return (
    <nav>
      <div className="setting-navigation relative ml-3">
        <>
          {MyResumePages ? (
            <>
              {navItems2.map((item) => {
                const isActive = pathname === item.href;

                return (
                  <div
                    key={item.id}
                    className={`${
                      isActive ? "setting-navigation-item" : ""
                    }  relative pb-12 last:pb-0 pl-6`}
                  >
                    <Link
                      href={item.href}
                      onClick={onNavigationClick}
                      className={cn(
                        " font-medium transition-colors",
                        isActive
                          ? "text-orange-100 font-bold"
                          : "text-gray-100 hover:text-orange-100"
                      )}
                    >
                      {item.label}
                    </Link>
                  </div>
                );
              })}
            </>
          ) : (
            <>
              {navItems.map((item) => {
                const isActive = pathname === item.href;

                return (
                  <div
                    key={item.id}
                    className={`${
                      isActive ? "setting-navigation-item" : ""
                    }  relative pb-12 last:pb-0 pl-6`}
                  >
                    <Link
                      href={item.href}
                      onClick={onNavigationClick}
                      className={cn(
                        " font-medium transition-colors",
                        isActive
                          ? "text-orange-100 font-bold"
                          : "text-gray-100 hover:text-orange-100"
                      )}
                    >
                      {item.label}
                    </Link>
                  </div>
                );
              })}
            </>
          )}
        </>
      </div>
    </nav>
  );
};

export default SettingNavigation;
