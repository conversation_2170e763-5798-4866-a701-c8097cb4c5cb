"use client";

import dynamic from "next/dynamic";
import Link from "next/link";
import { useState } from "react";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import Pagination from "@/components/Pagination/Pagination";
import JobNavigation from "@/components/Sections/JobNavigation";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetSavedCandidates } from "@/hooks/useQuery";

// Dynamically import the CandidateCardDashboard component with SSR disabled
const CandidateCardDashboard = dynamic(() => import("@/components/Cards/CandidateCardDashboard"), {
  ssr: false,
});

const navItems = [
  { href: "/company-dashboard/all-jobs", label: "All Jobs" },
  { href: "/company-dashboard/recently-posted", label: "Recently Posted" },
  { href: "/company-dashboard/saved-candidates", label: "Saved Candidates" },
];

export default function SaveCandidatesPage() {
  const [page, setPage] = useState(1);
  // const [limit, _setLimit] = useState(9);
  const limit = 9;
  const { data, isLoading, isError } = useGetSavedCandidates({ page, limit });
  const savedCandidates = data?.data.savedCandidates || [];
  const pagination = data?.data.pagination;

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // const handleLimitChange = (newLimit: number) => {
  //   setLimit(newLimit);
  //   setPage(1); // Reset to first page when changing limit
  // };

  return (
    <>
      <div className="flex flex-wrap gap-3 justify-between mb-7">
        <div>
          <h2 className="text-blue-100 text-3xl font-bold mb-3">Saved Candidates</h2>
          <p className="text-gray-100">Manage your saved candidates</p>
        </div>
        <div>
          <Link
            href={"/company-dashboard/post-job"}
            className="inline-flex justify-center items-center gap-x-2 rounded-full bg-blue-100 text-white px-10 py-3 text-lg font-bold"
          >
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="29"
                fill="none"
                viewBox="0 0 28 29"
              >
                <path
                  stroke="#fff"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M14 25c5.799 0 10.5-4.701 10.5-10.5S19.799 4 14 4 3.5 8.701 3.5 14.5 8.201 25 14 25M9 14.5h10M14 9.5v10"
                ></path>
              </svg>
            </span>
            <span>Post a Job</span>
          </Link>
        </div>
      </div>
      <div className="space-y-6">
        <JobNavigation navItems={navItems} />

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner />
          </div>
        ) : isError ? (
          <div className="text-center py-8 text-red-500">
            Error loading saved candidates. Please try again later.
          </div>
        ) : savedCandidates.length === 0 ? (
          <div className="text-center py-8">You haven&apos;t saved any candidates yet.</div>
        ) : (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mt-10">
              {savedCandidates.map((savedCandidate) => (
                <div key={savedCandidate._id}>
                  <CandidateCardDashboard
                    candidateId={savedCandidate.candidate._id}
                    candidateDescription={
                      savedCandidate.candidate.userProfile.shortBio || "No bio provided."
                    }
                    candidateImage={
                      savedCandidate.candidate.userProfile.profilePicture || DEFAULT_IMAGE
                    }
                    candidateLocation={
                      savedCandidate.candidate.userProfile.location?.city || "Not specified"
                    }
                    candidateName={`${savedCandidate.candidate.userProfile.firstName} ${savedCandidate.candidate.userProfile.lastName}`}
                    candidateProfessional={
                      savedCandidate?.candidate?.userProfile?.designation || "Job Seeker"
                    }
                    candidateSalary={"Not specified"}
                    link={`/for-recruiters/${savedCandidate.candidate._id}`}
                    // isProMember={savedCandidate.candidate.userId.isProMember}
                    _savedId={savedCandidate._id}
                    proMembershipEndsAt={savedCandidate?.user?.proMembershipEndsAt}
                    proTrialEndsAt={savedCandidate?.user?.proTrialEndsAt}
                  />
                </div>
              ))}
            </div>

            {pagination && pagination.pages > 1 && (
              <div className="mt-8 flex justify-center">
                <Pagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
}
