import { useMemo } from "react";
import { useGetCompanyProfile } from "./useQuery";
import {
  calculateCompanyProfileCompletion,
  getCompanyProfileCompletionDetails,
} from "@/utils/companyProfileCompletion";

/**
 * Custom hook to get company profile completion percentage and details
 * @returns Object with completion percentage, missing fields, and loading state
 */
export function useCompanyProfileCompletion() {
  const { data: companyProfileData, isLoading, error } = useGetCompanyProfile();

  const completionData = useMemo(() => {
    if (!companyProfileData?.data) {
      return {
        percentage: 0,
        missingFields: ["Complete company profile setup required"],
      };
    }

    return getCompanyProfileCompletionDetails(companyProfileData.data);
  }, [companyProfileData]);

  return {
    percentage: completionData.percentage,
    missingFields: completionData.missingFields,
    isLoading,
    error,
    companyData: companyProfileData?.data,
  };
}

/**
 * Custom hook to get only the company profile completion percentage
 * @returns The completion percentage (0-100)
 */
export function useCompanyProfileCompletionPercentage() {
  const { data: companyProfileData } = useGetCompanyProfile();

  return useMemo(() => {
    return calculateCompanyProfileCompletion(companyProfileData?.data);
  }, [companyProfileData]);
}
