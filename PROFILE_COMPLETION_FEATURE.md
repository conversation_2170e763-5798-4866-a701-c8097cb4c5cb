# Profile Completion Feature

This document describes the implementation of the profile completion percentage feature for the Yes Jobs Dashboard.

## Overview

The profile completion feature calculates and displays a percentage indicating how complete a jobseeker's profile is based on various profile fields and sections. This helps users understand what information they need to provide to have a complete profile, which can improve their job search success.

## Implementation

### Files Created/Modified

1. **`src/utils/profileCompletion.ts`** - Core utility functions for calculating profile completion
2. **`src/hooks/useProfileCompletion.ts`** - React hooks for profile completion functionality
3. **`src/components/Sections/ProfileCompletionTips.tsx`** - Component showing completion tips
4. **`src/components/Sections/DashboardSidebar.tsx`** - Updated to show dynamic completion percentage
5. **`src/utils/__tests__/profileCompletion.test.ts`** - Unit tests for the utility functions

### Core Functions

#### `calculateProfileCompletion(profileData: IProfileData | undefined): number`

Calculates the profile completion percentage (0-100) based on the following weighted criteria:

- **User Profile Fields (30%)**: firstName, lastName, phoneNo, dob, location, shortBio, profilePicture, designation
- **Skills (10%)**: At least one skill added
- **Job Preferences (15%)**: jobType, jobCategory, salaryRange, location
- **Work Experience (15%)**: At least one complete work experience entry
- **Education (10%)**: At least one academic experience entry
- **Certificates (5%)**: At least one certificate entry
- **CV Attachments (10%)**: At least one active CV uploaded
- **Portfolio Images (5%)**: At least one portfolio image uploaded

#### `getProfileCompletionDetails(profileData: IProfileData | undefined)`

Returns detailed information including:
- Completion percentage
- Array of missing fields with user-friendly names

### React Hooks

#### `useProfileCompletion()`

Returns:
- `percentage`: Completion percentage (0-100)
- `missingFields`: Array of missing field names
- `isLoading`: Loading state
- `error`: Error state
- `profileData`: Raw profile data

#### `useProfileCompletionPercentage()`

Returns only the completion percentage for simpler use cases.

### Components

#### `ProfileCompletionTips`

An expandable component that shows:
- Encouragement message when profile is incomplete
- List of missing fields (up to 5 shown, with count of additional fields)
- Congratulations message when profile is 100% complete

## Usage

### In DashboardSidebar

The profile completion percentage is automatically calculated and displayed for candidate dashboards:

```tsx
{CandidateDashboard && (
  <>
    <div className="bg-offWhite-100 py-3 px-2 mt-10">
      <p className="text-sm text-black-100 font-medium mb-3">Profile Completion</p>
      <p className="text-sm text-black-100 font-medium text-right mb-3">
        {profileCompletionPercentage}%
      </p>
      <div className="w-full bg-orange-100 bg-opacity-50 rounded-full h-2.5 mb-4">
        <div 
          className="bg-orange-100 h-2.5 rounded-full transition-all duration-300 ease-in-out" 
          style={{ width: `${profileCompletionPercentage}%` }}
        ></div>
      </div>
    </div>
    {profileCompletionPercentage < 100 && <ProfileCompletionTips />}
  </>
)}
```

### Using the Hooks

```tsx
import { useProfileCompletion } from '@/hooks/useProfileCompletion';

function MyComponent() {
  const { percentage, missingFields, isLoading } = useProfileCompletion();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <p>Profile is {percentage}% complete</p>
      {missingFields.length > 0 && (
        <ul>
          {missingFields.map(field => (
            <li key={field}>{field}</li>
          ))}
        </ul>
      )}
    </div>
  );
}
```

## Profile Schema Mapping

The feature maps the jobseeker profile schema fields as follows:

### Required User Profile Fields
- `userProfile.firstName` → "First Name"
- `userProfile.lastName` → "Last Name"
- `userProfile.phoneNo` → "Phone Number"
- `userProfile.dob` → "Date of Birth"
- `userProfile.location` → "Location"
- `userProfile.shortBio` → "Short Bio"
- `userProfile.profilePicture` → "Profile Picture"
- `userProfile.designation` → "Designation"

### Other Profile Sections
- `skills[]` → "Skills"
- `jobPreferences` → "Job Type Preference", "Job Category Preference", "Salary Range"
- `experiences[]` → "Work Experience"
- `academicExperiences[]` → "Education"
- `certificates[]` → "Certificates"
- `cvAttachments[]` → "CV/Resume"
- `portFolioImages[]` → "Portfolio Images"

## Features

1. **Real-time Updates**: Percentage updates automatically when profile data changes
2. **Smooth Animations**: Progress bar animates when percentage changes
3. **User-friendly Messages**: Clear indication of what fields are missing
4. **Conditional Display**: Only shows for candidate dashboards
5. **Expandable Tips**: Users can expand to see detailed missing fields
6. **Completion Celebration**: Special message when profile reaches 100%

## Testing

Unit tests are provided in `src/utils/__tests__/profileCompletion.test.ts` covering:
- Complete profile scenarios
- Incomplete profile scenarios
- Edge cases (undefined/null data)
- Percentage calculation accuracy
- Missing fields detection

## Future Enhancements

1. **Weighted Scoring**: Different fields could have different importance weights
2. **Profile Quality Score**: Beyond completion, assess quality of filled fields
3. **Completion Rewards**: Gamification elements for completing profile sections
4. **Smart Suggestions**: AI-powered suggestions for improving profile content
5. **Progress Tracking**: Historical tracking of completion progress over time