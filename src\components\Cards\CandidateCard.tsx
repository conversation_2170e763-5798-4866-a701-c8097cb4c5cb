"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { toast } from "sonner";
import { CurrencyIcon, LeftIconTop, LocationIcon1 } from "../Icons";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { useSaveCandidate, useUnsaveCandidate } from "@/hooks/useMutation";
import { useUserStore } from "@/store/useUserStore";

interface CandidateCardProps {
  candidateId: string;
  candidateImage: string;
  candidateName: string;
  candidateProfessional?: string;
  candidateLocation?: string;
  candidateSalary?: string;
  candidateSkills?: string[];
  candidateDescription: string;
  link: string;
  openJobs?: number;
  proMembershipEndsAt?: string;
  proTrialEndsAt?: string;
  isAllCompanies?: boolean;
  isSaved?: boolean;
  // jobsLink?: string;
}

const CandidateCard: React.FC<CandidateCardProps> = ({
  candidateId,
  candidateImage,
  candidateName,
  candidateProfessional,
  candidateLocation,
  candidateSalary,
  candidateSkills,
  candidateDescription,
  link,
  openJobs,
  proMembershipEndsAt,
  proTrialEndsAt,
  isAllCompanies = false,
  isSaved = false,
  // jobsLink,
}) => {
  const { mutate: saveCandidate, isPending: isSaving } = useSaveCandidate();
  const { mutate: unsaveCandidate, isPending: isUnsaving } = useUnsaveCandidate();
  const { currentUser } = useUserStore();
  const handleToggleSaveCandidate = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!candidateId) {
      return;
    }

    if (isSaved) {
      // Unsave candidate
      unsaveCandidate(candidateId, {
        onSuccess: () => {
          toast.success("Candidate removed from saved list");
        },
        onError: () => {
          toast.error("Failed to remove candidate from saved list");
        },
      });
    } else {
      // Save candidate
      saveCandidate(candidateId, {
        onSuccess: () => {
          toast.success("Candidate saved successfully");
        },
        onError: () => {
          toast.error("Failed to save candidate");
        },
      });
    }
  };
  return (
    <div className="p-6 border flex flex-col h-full border-gray-300 hover:border-orange-100 transition-all rounded-2xl shadow-md">
      <div className="flex items-center">
        <Image
          src={candidateImage}
          alt={candidateName}
          className="w-[100px] h-[100px] rounded-full"
          width={100}
          height={100}
        />
        <div className="ml-4">
          <div className="flex gap-x-3 items-center mb-3">
            <h2 className="text-2xl font-bold text-black-100">{candidateName}</h2>

            {(() => {
              const endsAt = proMembershipEndsAt ?? proTrialEndsAt;
              const isProMember = endsAt ? new Date(endsAt) > new Date() : false;

              return (
                isProMember && (
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="30"
                      height="30"
                      fill="none"
                      viewBox="0 0 30 30"
                    >
                      <rect width="30" height="30" fill="#007AB5" rx="15"></rect>
                      <path
                        fill="#fff"
                        d="M21.431 11.6a.99.99 0 0 0-1.181.237l-2.104 2.268-2.238-5.018V9.08a1 1 0 0 0-1.816 0v.006l-2.238 5.018-2.104-2.268a1 1 0 0 0-1.73.859l1.418 6.492a1 1 0 0 0 .982.812h9.16a1 1 0 0 0 .983-.812l1.417-6.492q-.001-.01.004-.02a.99.99 0 0 0-.553-1.076m-1.847 7.38-.003.02h-9.162l-.003-.02L9 12.5l.009.01 2.625 2.828a.5.5 0 0 0 .823-.137L15 9.5l2.543 5.703a.5.5 0 0 0 .824.136l2.625-2.827L21 12.5z"
                      ></path>
                    </svg>
                  </span>
                )
              );
            })()}

            {!isAllCompanies && currentUser && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleToggleSaveCandidate}
                      disabled={isSaving || isUnsaving}
                      className={`focus:outline-none transition-transform duration-200 ${isSaving || isUnsaving ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <path
                            fill={isSaved ? "#FF6B00" : "#262626"}
                            d={
                              isSaved
                                ? "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5z"
                                : "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                            }
                          ></path>
                        </svg>
                      </span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isSaved ? "Remove from Saved" : "Save Candidate"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          {candidateProfessional && (
            <p className="text-orange-100 font-medium ">{candidateProfessional}</p>
          )}
        </div>
      </div>
      <div className="flex my-6">
        {candidateLocation && (
          <div className="text-black-100 flex gap-x-2 ">
            <div>
              <LocationIcon1 />
            </div>
            {candidateLocation}
          </div>
        )}
        {candidateSalary && (
          <div className="text-black-100 flex gap-x-2 ml-6">
            <CurrencyIcon />
            {candidateSalary}
          </div>
        )}
      </div>
      {candidateSkills && (
        <div className="mb-6">
          <ul className="flex flex-wrap gap-2">
            <TooltipProvider>
              {candidateSkills.slice(0, 4).map((skill, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <li className="border w-32 truncate border-gray-300 inline-block text-gray-100 text-base font-normal px-6 py-3 rounded-full">
                      {skill}
                    </li>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{skill}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
            {candidateSkills.length > 4 && (
              <li className="bg-orange-100 text-white text-base font-normal flex justify-center items-center w-[60px] h-[44px] rounded-full">
                +{candidateSkills.length - 4}
              </li>
            )}
          </ul>
        </div>
      )}
      <div className="mb-4 h-[80px]">
        <p className="text-black-100 text-base break-words line-clamp-3">{candidateDescription}</p>
      </div>
      <div className="mt-auto">
        {openJobs ? (
          <div className="mb-4">
            <span className="bg-offWhite-100 text-orange-100 rounded-full px-5 py-2 inline-block">
              Open Jobs - {openJobs}
            </span>
          </div>
        ) : null}
        <div className="space-y-3">
          <Link
            href={link}
            className=" border border-orange-100 text-orange-100 hover:bg-orange-100 rounded-full hover:text-white p-4 flex justify-center items-center gap-x-2"
          >
            View Profile <LeftIconTop />
          </Link>
          {/* {isAllCompanies && jobsLink && (
            <Link
              href={jobsLink}
              className="bg-orange-100 text-white hover:bg-orange-200 rounded-full p-4 flex justify-center items-center gap-x-2"
            >
              View Jobs <LeftIconTop />
            </Link>
          )} */}
        </div>
      </div>
    </div>
  );
};

export default CandidateCard;
