import AboutUsHome from "@/components/Sections/AboutUsHome";
import CTASection from "@/components/Sections/CTASection";
import FeaturesSection from "@/components/Sections/FeaturesSection";
import HeroSection from "@/components/Sections/HeroSection";
import JobsSection from "@/components/Sections/JobsSection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import SapportCompaniesSection from "@/components/Sections/SapportCompaniesSection";
import TopCompaniesSection from "@/components/Sections/TopCompaniesSection";
import type { Metadata } from "next";
export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - home",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};
export default function Home() {
  return (
    <>
      <HeroSection />
      <AboutUsHome />
      <FeaturesSection />
      <JobsSection />
      <TopCompaniesSection />
      <SapportCompaniesSection />
      <CTASection />
      <MobileAppSection />
    </>
  );
}
