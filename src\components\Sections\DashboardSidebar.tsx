"use client";
import Image from "next/image";
import React, { useState, useMemo } from "react";
import { toast } from "sonner";
import { CrownIcon } from "../Icons";
import { Button } from "../ui/button";
import SidebarNavCandidate from "./SidebarNavCandidate";
import SidebarNavCompany from "./SidebarNavCompany";
import SkillsList from "./SkillsList";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { usePurchasePro } from "@/hooks/useMutation";
import { useGetAdminSettings } from "@/hooks/useQuery";
import { useProfileCompletionPercentage } from "@/hooks/useProfileCompletion";
import { useCompanyProfileCompletionPercentage } from "@/hooks/useCompanyProfileCompletion";
import { ApiError } from "@/types/common.types";
import { IPurchaseProResponseDto } from "@/types/mutation.types";
import { useFirebasePushNotifications } from "@/utils/notifications";
import { useRestriction } from "@/hooks/useRestriction";

interface IDashboardSidebar {
  image: string;
  candidateName: string;
  candidateDesignation: string;
  getFeatured: boolean;
  candidateSkills?: string[];
  CandidateDashboard?: boolean;
  CompanyDashboard?: boolean;
  proMembershipEndsAt?: string;
  proTrialEndsAt?: string;
}

const DashboardSidebar = ({
  image,
  candidateName,
  candidateDesignation,
  candidateSkills,
  CandidateDashboard,
  CompanyDashboard,
  getFeatured,
  proMembershipEndsAt,
  proTrialEndsAt,
}: IDashboardSidebar) => {
  useFirebasePushNotifications();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isPurchaseProModalOpen, setIsPurchaseProModalOpen] = useState(false);
  const [selectedWeeks, setSelectedWeeks] = useState(4); // Default to 4 weeks as per request body

  const { data: adminSettingsData } = useGetAdminSettings();
  const proMemberPricePerWeek = adminSettingsData?.data?.ProMemberPricePerWeek || 0;

  // Get profile completion percentage for candidate dashboard
  const profileCompletionPercentage = useProfileCompletionPercentage();

  // Get company profile completion percentage for company dashboard
  const companyProfileCompletionPercentage = useCompanyProfileCompletionPercentage();

  const { mutate: purchasePro, isPending: isPurchasingPro } = usePurchasePro();
  const { canAccessPremium, isRestricted } = useRestriction();

  const purchaseProPrice = useMemo(() => {
    return proMemberPricePerWeek * selectedWeeks;
  }, [proMemberPricePerWeek, selectedWeeks]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handlePurchasePro = () => {
    if (isRestricted) {
      toast.error("Your account is restricted by admin. Contact admin for more details.");
      return;
    }
    setIsPurchaseProModalOpen(true);
  };

  const confirmPurchasePro = () => {
    purchasePro(
      { weeks: selectedWeeks },
      {
        onSuccess: (data: IPurchaseProResponseDto) => {
          setIsPurchaseProModalOpen(false);
          setSelectedWeeks(4); // Reset selected weeks
          if (data?.data?.url) {
            window.open(data.data.url, "_blank");
          }
        },
        onError: (error: ApiError) => {
          toast.error(error.response?.data?.message || "Failed to purchase pro membership");
        },
      }
    );
  };

  const cancelPurchasePro = () => {
    setIsPurchaseProModalOpen(false);
    setSelectedWeeks(4); // Reset selected weeks
  };

  // useEffect(() => {
  //   const initializeNotifications = async () => {
  //     if (typeof window === "undefined") return;

  //     try {
  //       const { requestFirebaseNotificationPermission, listenToForegroundMessages } = await import(
  //         "@/utils/notifications"
  //       );
  //       await requestFirebaseNotificationPermission();
  //       await listenToForegroundMessages();
  //       console.log("DashboardSidebar notifications initialized");
  //     } catch (error) {
  //       console.error("Failed to initialize notifications in DashboardSidebar:", error);
  //     }
  //   };

  //   initializeNotifications();
  // }, []);

  return (
    <>
      <button
        onClick={toggleSidebar}
        className="lg:hidden  bg-orange-100 text-white px-4 py-2 inline-block mb-4 rounded-full"
      >
        Profile Menu
      </button>
      <div
        className={`fixed inset-0  bg-black bg-opacity-50 transition-opacity ${
          isSidebarOpen ? "opacity-100 z-40" : "opacity-0 pointer-events-none"
        } lg:hidden`}
        onClick={toggleSidebar}
      ></div>
      <div
        className={`fixed inset-y-0 left-0  w-[350px] bg-white shadow-lg transform transition-transform ${
          isSidebarOpen ? "translate-x-0 overflow-y-auto z-50" : "-translate-x-full"
        } lg:relative lg:translate-x-0 lg:w-[350px] lg:pt-10 px-3 lg:pb-14 lg:h-fit`}
      >
        <div className="lg:hidden flex items-center justify-between p-4 border-b">
          <button onClick={toggleSidebar} className="text-gray-800 focus:outline-none">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
        <Image
          src={image}
          alt={`${candidateName} Image`}
          width={120}
          height={120}
          className="w-[120px] h-[120px] rounded-full border border-orange-100 object-cover mx-auto mt-10"
        />
        <div className="text-center mt-6">
          <h1 className="text-2xl font-bold text-black-100 mb-2">{candidateName}</h1>
          <h2 className="text-orange-100 font-medium font-sm mb-6">{candidateDesignation}</h2>
          {getFeatured && (
            <>
              {(() => {
                const endsAt = proMembershipEndsAt ?? proTrialEndsAt;
                const isProMember = endsAt ? new Date(endsAt) > new Date() : false;

                return isProMember ? (
                  <div className="w-[48px] h-[48px] rounded-full inline-flex justify-center items-center bg-blue-300 text-sm text-blue-100 font-bold">
                    <CrownIcon />
                  </div>
                ) : (
                  <Button
                    variant="default"
                    className="rounded-full gap-x-2 bg-blue-300 px-3 py-2 text-sm text-blue-100 font-bold"
                    onClick={handlePurchasePro}
                    disabled={isPurchasingPro || !canAccessPremium}
                  >
                    <CrownIcon /> Get Featured
                  </Button>
                );
              })()}
            </>
          )}
        </div>
        {CandidateDashboard && (
          <>
            <div className="bg-offWhite-100 py-3 px-2 mt-10">
              <p className="text-sm text-black-100 font-medium mb-3">Profile Completion</p>
              <p className="text-sm text-black-100 font-medium text-right mb-3">
                {profileCompletionPercentage}%
              </p>
              <div className="w-full bg-orange-100 bg-opacity-50 rounded-full h-2.5 mb-4">
                <div
                  className="bg-orange-100 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${profileCompletionPercentage}%` }}
                ></div>
              </div>
            </div>
            {/* {profileCompletionPercentage < 100 && <ProfileCompletionTips />} */}
          </>
        )}
        {CompanyDashboard && (
          <>
            <div className="bg-offWhite-100 py-3 px-2 mt-10">
              <p className="text-sm text-black-100 font-medium mb-3">Profile Completion</p>
              <p className="text-sm text-black-100 font-medium text-right mb-3">
                {companyProfileCompletionPercentage}%
              </p>
              <div className="w-full bg-orange-100 bg-opacity-50 rounded-full h-2.5 mb-4">
                <div
                  className="bg-orange-100 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${companyProfileCompletionPercentage}%` }}
                ></div>
              </div>
            </div>
            {/* {companyProfileCompletionPercentage < 100 && <CompanyProfileCompletionTips />} */}
          </>
        )}
        <hr className="my-5" />
        {CandidateDashboard && <SidebarNavCandidate />}
        {CompanyDashboard && <SidebarNavCompany />}

        {candidateSkills && (
          <>
            <hr className="my-5" />
            <div>
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-black-100 font-medium">Job Skills</h3>
                </div>
                {/* <div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="25"
                    height="25"
                    fill="none"
                    viewBox="0 0 25 25"
                  >
                    <circle cx="12.5" cy="12.5" r="12" fill="#fff" stroke="#EC761E"></circle>
                    <path
                      stroke="#EC761E"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12.143 17.515H17.5"
                    ></path>
                    <path
                      fill="#EC761E"
                      d="M14.821 6.682c.237-.263.558-.41.893-.41.166 0 .33.036.483.106.154.07.293.174.41.304s.21.285.274.456a1.54 1.54 0 0 1 0 1.073c-.064.17-.157.325-.274.456l-7.44 8.267-2.381.661.595-2.645z"
                    ></path>
                  </svg>
                </div> */}
              </div>
              <SkillsList candidateSkills={candidateSkills} />
            </div>
          </>
        )}
      </div>

      <Dialog open={isPurchaseProModalOpen} onOpenChange={setIsPurchaseProModalOpen}>
        <DialogContent className="w-full max-w-2xl p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">
              Become a Pro Member and unlock exclusive features!
            </DialogTitle>
            <DialogDescription className="text-gray-700">
              Current price per week: ${proMemberPricePerWeek}
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 pt-0">
            <div className="mb-6">
              <label htmlFor="weeks" className="block text-sm font-medium text-gray-700 mb-2">
                Select duration:
              </label>
              <select
                id="weeks"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base sm:text-sm rounded-md border-orange-100 border text-orange-100 h-[44px] cursor-pointer"
                value={selectedWeeks}
                onChange={(e) => setSelectedWeeks(Number(e.target.value))}
              >
                <option value={1}>1 Week</option>
                <option value={2}>2 Weeks</option>
                <option value={3}>3 Weeks</option>
                <option value={4}>4 Weeks</option>
              </select>
            </div>
            <p className="text-lg font-bold mb-6">Total Price: ${purchaseProPrice}</p>
          </div>
          <DialogFooter className="p-6 pt-0 flex justify-end gap-4">
            <button
              onClick={cancelPurchasePro}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={confirmPurchasePro}
              className="px-4 py-2 bg-orange-100 text-white rounded-md hover:bg-orange-200"
              disabled={isPurchasingPro}
            >
              {isPurchasingPro ? "Processing..." : "Confirm Purchase"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DashboardSidebar;
