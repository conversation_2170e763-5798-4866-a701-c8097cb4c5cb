"use client";
import { TooltipProvider } from "@radix-ui/react-tooltip";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

interface JobCardProps {
  imageUrl: string;
  companyName: string;
  jobTitle: string;
  jobTags: string[];
  payRate: string;
  location: string;
  onDetailClick: () => void;
  timePeriod: string;
}

const bgColorClasses = ["bg-offWhite-200", "bg-offWhite-300", "bg-offWhite-400"];

const JobCard: React.FC<JobCardProps> = ({
  imageUrl,
  companyName,
  jobTitle,
  jobTags,
  payRate,
  location,
  onDetailClick,
  timePeriod,
}) => {
  const [bgColorClass, setBgColorClass] = useState(bgColorClasses[0]);

  useEffect(() => {
    const index = Math.floor(Math.random() * bgColorClasses.length);
    setBgColorClass(bgColorClasses[index]);
  }, []);

  return (
    <div className="border border-[#000] rounded-3xl h-full">
      <div className={`${bgColorClass} p-7 m-2 rounded-3xl`}>
        <div className="flex items-center ">
          <div className="w-[60px] h-[60px] rounded-full bg-white flex items-center justify-center mr-5">
            <Image src={imageUrl} alt={`${companyName} logo`} width={37} height={37} />
          </div>
          <div>
            <h3 className="text-[#000]">{companyName}</h3>
          </div>
          <div className="ml-auto bg-white w-[40px] h-[40px] rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="22"
              fill="none"
              viewBox="0 0 18 22"
            >
              <path
                stroke="#737373"
                d="M17 15.092v-4.994c0-4.29 0-6.434-1.172-7.766S12.772 1 9 1 3.344 1 2.172 2.332 1 5.808 1 10.1v4.994c0 3.096 0 4.644.652 5.32a1.9 1.9 0 0 0 1.124.58c.876.112 1.9-.906 3.95-2.946.906-.9 1.36-1.352 1.884-1.47a1.8 1.8 0 0 1 .78 0c.524.118.978.568 1.884 1.47 2.048 2.04 3.074 3.06 3.952 2.946a1.9 1.9 0 0 0 1.12-.58c.654-.676.654-2.224.654-5.32z"
              ></path>
            </svg>
          </div>
        </div>
        <div className="job-card-title my-6">
          <h2 className="text-[#000] text-2xl capitalize">{jobTitle}</h2>
        </div>
        <div className="job-card-tags">
          <div className="flex flex-wrap gap-3">
            {/* {jobTags.map((tag, index) => (
              <span
                key={index}
                className="text-gray-100 border border-gray-100 rounded-full px-4 py-2 "
              >
                {tag}
              </span>
            ))} */}

            <TooltipProvider>
              {jobTags.slice(0, 4).map((skill, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <span className="text-gray-100 border border-gray-100 rounded-full px-4 py-2 w-32 truncate">
                      {skill}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{skill}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </TooltipProvider>
            {jobTags.length > 4 && (
              <span className="bg-orange-100 text-white text-base font-normal flex justify-center items-center w-[60px] h-[44px] rounded-full">
                +{jobTags.length - 4}
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center  flex-wrap gap-3 px-10 py-5">
        <div>
          <p className="text-2xl font-medium mb-1">
            ${payRate}/{timePeriod}
          </p>
          <p className="text-gray-100 text-sm">{location}</p>
        </div>
        <div className="ms-auto">
          <button
            onClick={onDetailClick}
            className="py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 bg-[#000] text-white"
          >
            <span className="text">Details</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default JobCard;
