"use client";

import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
import { BriefcaseIcon, UserIcon } from "@/components/Icons";
import CandidateSignupForm from "@/components/forms/CandidateSignupForm";
import RecruiterSignupForm from "@/components/forms/RecruiterSignupForm";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function SignupPage() {
  return (
    <div>
      <div className="text-center mb-6">
        <PrimaryHeading>
          <span>Create an account</span>{" "}
        </PrimaryHeading>
        {/* <p className="text-gray-100 text-lg mt-3">Lorem ipsum dolor sit amet adipiscing elit.</p> */}
      </div>

      <Tabs defaultValue="candidate">
        <TabsList className="grid w-full grid-cols-2 bg-transparent">
          <TabsTrigger
            value="candidate"
            className={
              "data-[state=active]:bg-orange-100 data-[state=active]:text-white rounded-full py-3 px-6 text-lg flex gap-x-2"
            }
          >
            <UserIcon /> Candidate
          </TabsTrigger>
          <TabsTrigger
            value="recruiters"
            className={
              "data-[state=active]:bg-orange-100 data-[state=active]:text-white rounded-full py-3 px-6 text-lg flex gap-x-2"
            }
          >
            <BriefcaseIcon /> Recruiters
          </TabsTrigger>
        </TabsList>
        <div className="mt-14">
          <TabsContent value="candidate">
            <CandidateSignupForm />
          </TabsContent>
          <TabsContent value="recruiters">
            <RecruiterSignupForm />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
