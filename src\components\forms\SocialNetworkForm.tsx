"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, type SubmitHand<PERSON>, Controller } from "react-hook-form";
import { toast } from "sonner";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateCompanyProfile } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { ICompany, ISocialNetwork } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const topSocialNetworks = [
  "Facebook",
  "Instagram",
  "Twitter",
  "LinkedIn",
  "YouTube",
  "TikTok",
  "Pinterest",
  "Snapchat",
  "Discord",
  "Reddit",
];

const SocialNetworkForm = ({ isButton = true }: { isButton?: boolean }) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading, refetch } = useGetCompanyProfile(); // Add `isLoading` to handle loading state
  const companyProfile = companyData?.data;
  const [isOpen, setIsOpen] = useState(false);
  const [networks, setNetworks] = useState<ISocialNetwork[]>([]);

  // Sync networks state with companyProfile data
  useEffect(() => {
    if (companyProfile?.socialNetworks) {
      setNetworks(companyProfile.socialNetworks);
    }
  }, [companyProfile]);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<Omit<ISocialNetwork, "_id">>({
    defaultValues: {
      networkName: "",
      networkUrl: "",
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateCompanyProfile({
    onSuccess: () => {
      toast.success("Social networks updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] }); // Invalidate query to refresh data
      refetch(); // Explicitly refetch the company profile
      setIsOpen(false);
      reset();
    },
    onError: (error) => {
      console.log({ error });
      toast.error(error.response?.data?.message || "Failed to update social networks");
    },
  });

  const onSubmit: SubmitHandler<Omit<ISocialNetwork, "_id">> = async (formData) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const newNetwork: ISocialNetwork = {
      ...formData,
      _id: Date.now().toString(), // Temporary ID for new networks
    };

    const updatedNetworks = [...networks, newNetwork];
    setNetworks(updatedNetworks);

    const updatedProfile: Partial<ICompany> = {
      socialNetworks: updatedNetworks,
    };

    updateProfile(updatedProfile);
  };

  const handleDelete = (index: number) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const updatedNetworks = networks.filter((_, i) => i !== index);
    setNetworks(updatedNetworks);

    const updatedProfile: Partial<ICompany> = {
      socialNetworks: updatedNetworks,
    };

    updateProfile(updatedProfile);
  };

  return (
    <div>
      <div className="flex flex-wrap items-center justify-between mb-10">
        <div>
          <h2 className="text-blue-200 text-2xl font-bold">Company</h2>
          <h3 className="text-4xl font-medium text-black-100 mt-3">Social Network</h3>
        </div>
        <div>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                fill="none"
                viewBox="0 0 30 30"
              >
                <path
                  stroke="#EC761E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                ></path>
              </svg>
              Add Network
            </DialogTrigger>
            <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
              <DialogHeader className="text-left">
                <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                  Add Social Network
                </DialogTitle>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="mb-6">
                    <Label htmlFor="networkName" className="mb-3 block">
                      Network Name
                    </Label>
                    <Controller
                      name="networkName"
                      control={control}
                      rules={{ required: "Network name is required" }}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value}>
                          <SelectTrigger className={inputClasses}>
                            <SelectValue placeholder="Select a social network" />
                          </SelectTrigger>
                          <SelectContent>
                            {topSocialNetworks.map((network) => (
                              <SelectItem key={network} value={network}>
                                {network}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.networkName && (
                      <p className="text-red-500 text-sm">{errors.networkName.message}</p>
                    )}
                  </div>
                  <div className="mb-6">
                    <Label htmlFor="networkUrl" className="mb-3 block">
                      URL
                    </Label>
                    <Input
                      className={inputClasses}
                      id="networkUrl"
                      type="url"
                      {...register("networkUrl", {
                        required: "URL is required",
                        pattern: {
                          value: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
                          message: "Please enter a valid URL",
                        },
                      })}
                    />
                    {errors.networkUrl && (
                      <p className="text-red-500 text-sm">{errors.networkUrl.message}</p>
                    )}
                  </div>

                  <div className="flex gap-x-5 mt-10">
                    <button
                      type="button"
                      onClick={() => setIsOpen(false)}
                      className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={isPending}
                      type="submit"
                      className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
                    >
                      {isPending ? "Loading..." : "Save Details"}
                    </button>
                  </div>
                </form>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-4">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
            <p className="text-gray-500 mt-4">Loading social networks...</p>
          </div>
        ) : networks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No social networks added yet. Click &quot;Add Network&quot; to add one.
          </div>
        ) : (
          networks.map((network, index) => (
            <div
              key={network._id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div>
                <h4 className="font-medium">{network.networkName}</h4>
                <a
                  href={network.networkUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {network.networkUrl}
                </a>
              </div>
              <button
                onClick={() => handleDelete(index)}
                className="text-red-500 hover:text-red-700"
              >
                Delete
              </button>
            </div>
          ))
        )}
      </div>
      {isButton && (
        <div className="flex gap-5 mt-10">
          <button
            onClick={() => router.back()}
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
          >
            Go Back
          </button>
          <button
            disabled={isPending}
            onClick={() => router.push("/company-profile-completion?stepId=company-photos")}
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
          >
            {isPending ? "Loading..." : "Next Step"}
          </button>
        </div>
      )}
    </div>
  );
};

export default SocialNetworkForm;
