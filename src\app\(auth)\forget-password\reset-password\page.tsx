import ResetPasswordForm from "@/components/forms/ResetPasswordForm";
import type { Metadata } from "next";
export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - reset password",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};
// Page components in the App Router automatically receive searchParams
export default function ResetPasswordPage({
  searchParams,
}: {
  searchParams: { code?: string; userId?: string };
}) {
  const code = searchParams.code || "";
  const userId = searchParams.userId || "";

  return (
    <>
      <ResetPasswordForm code={code} userId={userId} />
    </>
  );
}
