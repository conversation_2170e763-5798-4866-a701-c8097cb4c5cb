"use client";
import React, { useEffect } from "react";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
// import Link from "next/link";
import SecondaryButton from "../Buttons/SecondaryButton";
import CompanyCard from "../Cards/CompanyCard";
import CustomTag from "../CustomTag";
import { PrimaryHeading } from "../Headings/PrimaryHeading";
import LoadingSpinner from "../LoadingSpinner/LoadingSpinner";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetAllCompanies, useGetCompaniesSection } from "@/hooks/useQuery";
import "swiper/css";
import "swiper/css/autoplay";

const TopCompaniesSection = () => {
  // Fetch companies data with a limit of 10
  const { data, isLoading, isError, refetch } = useGetAllCompanies(
    {
      limit: 10,
      page: 1,
    },
    { refetchOnWindowFocus: false }
  );

  // Extract companies from the response
  const companies = data?.data?.allCompanies || [];

  // Refetch on component mount
  useEffect(() => {
    refetch();
  }, [refetch]);

  const { data: companiesSectionData } = useGetCompaniesSection();

  return (
    <section className="lg:py-24 py-14">
      <div className="container mx-auto">
        <div className="grid lg:grid-cols-2 items-end lg:px-10">
          <div>
            <CustomTag>{companiesSectionData?.data.section.subheading}</CustomTag>
            <PrimaryHeading className="mt-12">
              {/* Top Workplaces for <br /> Professionals in <span>2025</span> */}
              {companiesSectionData?.data.section.heading}
            </PrimaryHeading>
          </div>
          <div className="lg:max-w-[420px] lg:ml-auto">
            <p className="text-gray-100 mb-10">{companiesSectionData?.data.section.description}</p>
            <div className="lg:text-right">
              <SecondaryButton href="/all-companies" variant="bordered">
                Show More
              </SecondaryButton>
            </div>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-16">
          <LoadingSpinner />
        </div>
      ) : isError ? (
        <div className="text-center py-16 text-red-500">
          Error loading companies. Please try again later.
        </div>
      ) : companies.length === 0 ? (
        <div className="text-center py-16 text-gray-100">
          No companies found. Check back later for updates.
        </div>
      ) : (
        <Swiper
          modules={[Autoplay]}
          spaceBetween={30}
          autoplay={{
            delay: 2500,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 1,
            },
            768: {
              slidesPerView: 3,
            },
            1024: {
              slidesPerView: 4,
            },
          }}
          className="mySwiper lg:mt-16"
        >
          {companies.map((company) => (
            <SwiperSlide key={company._id}>
              <CompanyCard
                companyName={company.companyProfile.companyName}
                description={
                  company.aboutCompany?.description || "Company description not available."
                }
                href={`/all-companies/${company.companyProfile.slug || company._id}`}
                imageUrl={company.companyProfile.profilePicture || DEFAULT_IMAGE}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      )}
    </section>
  );
};

export default TopCompaniesSection;
