import Link from "next/link";
import DeactivateAccountButton from "./DeactivateAccountButton";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
export default function PasswordSecurity() {
  return (
    <>
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/applied-jobs">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <h2 className="text-orange-100 text-3xl font-bold mb-10">Password & Security</h2>
      <div className="mb-10 border border-gray-300 p-6 rounded-2xl">
        <div className="sm:flex justify-between items-center pb-4 border-b border-gray-300">
          <div>
            <h4 className="text-black-100 text-lg font-bold mb-2">Password</h4>
            <p className="text-gray-100 font-sm font-normal leading-6">
              Create a strong and unique password to protect your account. A secure password helps
              keep your personal information and team data safe.
            </p>
          </div>
          <div className="xl:w-[400px] md:w-[600px] ml-auto text-end mt-4 sm:mt-0">
            <Link
              href="/settings/password-security/change-password"
              className="px-5 py-2 rounded-full text-gray-100 bg-gray-300 inline-flex items-center justify-end gap-x-2"
            >
              Change Password{" "}
              <span>
                {" "}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="17"
                  fill="none"
                  viewBox="0 0 16 17"
                >
                  <path
                    fill="#737373"
                    d="M13 5.5H6V4a2 2 0 0 1 2-2c.96 0 1.825.688 2.01 1.6a.5.5 0 0 0 .98-.2C10.707 2.01 9.45 1 8 1a3.003 3.003 0 0 0-3 3v1.5H3a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-7a1 1 0 0 0-1-1m0 8H3v-7h10zM8.75 10a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0"
                  ></path>
                </svg>
              </span>{" "}
            </Link>
          </div>
        </div>
        <div className="pt-6 sm:flex justify-between items-center pb-4 border-b border-gray-300">
          <div>
            <h4 className="text-black-100 text-lg font-bold mb-2">Deactivate my account</h4>
            <p className="text-gray-100 font-sm font-normal leading-6">
              By shutting down your account, it will be temporarily deactivated. You can reactivate
              it anytime by signing in again. None of your data will be lost during this process.
            </p>
          </div>
          <div className="xl:w-[400px] md:w-[600px] ml-auto text-end end mt-4 sm:mt-0">
            <DeactivateAccountButton />
          </div>
        </div>
        <Link
          href={"/terms-and-conditions"}
          className="pt-6 flex justify-between items-center pb-4 border-b border-gray-300"
        >
          <div>
            <h4 className="text-black-100 text-lg font-bold">Terms & Conditions</h4>
          </div>
          <div className="text-end">
            <div className="w-8 h-8 rounded-full text-gray-100 bg-gray-300 inline-flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                viewBox="0 0 16 16"
              >
                <path
                  fill="#737373"
                  d="m11.53 8.53-5 5a.751.751 0 0 1-1.062-1.062L9.938 8 5.468 3.53a.751.751 0 1 1 1.063-1.062l5 5a.75.75 0 0 1-.001 1.063"
                ></path>
              </svg>
            </div>
          </div>
        </Link>
        <Link href={"/privacy-policy"} className="pt-6 flex justify-between items-center">
          <div>
            <h4 className="text-black-100 text-lg font-bold">Privacy Policy</h4>
          </div>
          <div className="text-end">
            <div className="w-8 h-8 rounded-full text-gray-100 bg-gray-300 inline-flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="none"
                viewBox="0 0 16 16"
              >
                <path
                  fill="#737373"
                  d="m11.53 8.53-5 5a.751.751 0 0 1-1.062-1.062L9.938 8 5.468 3.53a.751.751 0 1 1 1.063-1.062l5 5a.75.75 0 0 1-.001 1.063"
                ></path>
              </svg>
            </div>
          </div>
        </Link>
      </div>
    </>
  );
}
