"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";
import { toast } from "sonner";
import FullPageLoader from "@/components/ui/FullPageLoader";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { useLoader } from "@/context/LoaderContext";
import {
  useLogout,
  usePurchasePro,
  useMarkAllNotificationsAsRead,
  useNotificationReadById,
} from "@/hooks/useMutation";
import {
  useGetConversations,
  useGetAdminSettings,
  useGetNotifications,
  useGetUnreadNotificationsCount,
} from "@/hooks/useQuery";
import { useGetCurrentUser } from "@/hooks/useQuery";
import { removeFirebaseToken } from "@/service/mutation.service";
import {
  markNotificationAsReadSocket,
  markAllNotificationsAsReadSocket,
} from "@/service/socket.service";
import { useNotificationStore } from "@/store/useNotificationStore";
import { useUserStore } from "@/store/useUserStore";
import { useMessageCountStore } from "@/store/useMessageCountStore";
import { ApiError, UserRole } from "@/types/common.types";
import { IPurchaseProResponseDto } from "@/types/mutation.types";
import { CurrentCompanyType, CurrentJobSeekerType, INotification } from "@/types/query.types";
import DesktopHeader from "./DesktopHeader";
import MobileHeader from "./MobileHeader";
import { useMessagePageVisibility } from "@/hooks/useMessagePageVisibility";
import { useHydrateMessageCountStore } from "@/hooks/useHydrateMessageCountStore";

const Header = ({ dashboardPages = false }: { dashboardPages?: boolean }) => {
  const { currentUser, hasHydrated, logout: clearStore } = useUserStore();
  const { showLoader, hideLoader } = useLoader();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isPurchaseProModalOpen, setIsPurchaseProModalOpen] = useState(false);
  const [selectedWeeks, setSelectedWeeks] = useState(1); // Default to 4 weeks as per request body
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Hydrate message count store and track page visibility
  useHydrateMessageCountStore();
  useMessagePageVisibility();

  // Get message count from store
  const { totalUnreadCount, shouldShowCount, setTotalUnreadCount } = useMessageCountStore();

  // Debug logging
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("[Header] Message count state:", {
        totalUnreadCount,
        shouldShowCount,
        pathname: typeof window !== "undefined" ? window.location.pathname : "",
      });
    }
  }, [totalUnreadCount, shouldShowCount]);

  const { data: adminSettingsData } = useGetAdminSettings();
  const proMemberPricePerWeek = adminSettingsData?.data?.ProMemberPricePerWeek || 0;

  const { mutate: purchasePro, isPending: isPurchasingPro } = usePurchasePro();

  const purchaseProPrice = useMemo(() => {
    return proMemberPricePerWeek * selectedWeeks;
  }, [proMemberPricePerWeek, selectedWeeks]);

  // Move all hook calls before any conditional returns
  const { data: userData } = useGetCurrentUser({
    enabled: !!currentUser && hasHydrated,
  });

  // Get conversations to count unread messages
  const { data: conversationsData } = useGetConversations(
    {},
    {
      enabled: !!currentUser && hasHydrated,
    }
  );

  // Get notifications and unread count
  const { data: notificationsData } = useGetNotifications(
    { page: 1, limit: 10 },
    {
      enabled: !!currentUser && hasHydrated,
    }
  );

  const { data: unreadNotificationsCountData } = useGetUnreadNotificationsCount({
    enabled: !!currentUser && hasHydrated,
  });

  // Get notification store state
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotificationStore();

  // Mutation for marking all notifications as read
  const { mutate: markAllNotificationsAsReadMutation } = useMarkAllNotificationsAsRead({
    onSuccess: () => {
      // Use socket event for real-time updates
      markAllNotificationsAsReadSocket().catch((error) => {
        console.error("Failed to emit mark all notifications as read socket event:", error);
      });
    },
  });

  const { mutate: markNotificationAsReadMutation } = useNotificationReadById({
    onSuccess: (data) => {
      // Use socket event for real-time updates
      markNotificationAsReadSocket(data.data.notification._id).catch((error) => {
        console.error("Failed to emit mark notification as read socket event:", error);
      });
    },
  });

  // Calculate unread message count and sync with store
  useEffect(() => {
    if (conversationsData?.data?.conversations) {
      const totalUnread = conversationsData.data.conversations.reduce(
        (total, conversation) => total + (conversation.unreadCount || 0),
        0
      );
      setTotalUnreadCount(totalUnread);
    }
  }, [conversationsData, setTotalUnreadCount]);

  // Sync notification store with API data
  useEffect(() => {
    if (notificationsData?.data?.notifications) {
      // Update store with API notifications
      const { setNotifications } = useNotificationStore.getState();
      setNotifications(notificationsData.data.notifications);
    }
  }, [notificationsData]);

  // Sync unread count with API data
  useEffect(() => {
    if (unreadNotificationsCountData?.data?.count !== undefined) {
      // Update store with API unread count
      const { setUnreadCount } = useNotificationStore.getState();
      setUnreadCount(unreadNotificationsCountData.data.count);
    }
  }, [unreadNotificationsCountData]);

  const { mutate: logout } = useLogout({
    onSuccess: () => {
      clearStore();
      queryClient.clear();
      toast.success("Logged out successfully", {
        description: "You have been logged out of your account.",
      });

      setTimeout(() => {
        hideLoader();
        router.push("/login");
      }, 1500);
    },
    onError: (error) => {
      hideLoader();
      toast.error("Logout failed", {
        description: error.response?.data?.message || "Something went wrong. Please try again.",
      });
    },
  });

  // Now we can safely do conditional returns
  if (!hasHydrated) {
    return <FullPageLoader message="Loading..." />;
  }

  const handleLogout = async () => {
    showLoader("Logging you out...");
    await logout();
    const token = localStorage.getItem("fcmToken");
    if (token) await removeFirebaseToken({ token });
  };

  const handlePurchasePro = () => {
    setIsPurchaseProModalOpen(true);
  };

  // Handle notification click
  const handleNotificationClick = async (notification: INotification) => {
    try {
      // Mark as read in store
      markAsRead(notification._id);
      markNotificationAsReadMutation(notification._id);

      // Use socket event for real-time updates
      await markNotificationAsReadSocket(notification._id);

      // Navigate based on notification type
      if (notification.type === "JOB_APPLICATION" && notification.metadata?.jobId) {
        router.push(`/company-dashboard/all-jobs/${notification.metadata.jobId}`);
      } else if (notification.type === "SHORTLISTED" && notification.metadata?.jobId) {
        router.push(`/dashboard/shortlisted-jobs/${notification.metadata.jobId}`);
      }
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Handle mark all notifications as read
  const handleMarkAllAsRead = () => {
    // Mark all as read in store
    markAllAsRead();

    // Use mutation and socket event
    markAllNotificationsAsReadMutation();
  };

  const confirmPurchasePro = () => {
    purchasePro(
      { weeks: selectedWeeks },
      {
        onSuccess: (data: IPurchaseProResponseDto) => {
          setIsPurchaseProModalOpen(false);
          setSelectedWeeks(4); // Reset selected weeks
          if (data?.data?.url) {
            window.open(data.data.url, "_blank");
          }
        },
        onError: (error: ApiError) => {
          toast.error(error.response?.data?.message || "Failed to purchase pro membership");
        },
      }
    );
  };

  const cancelPurchasePro = () => {
    setIsPurchaseProModalOpen(false);
    setSelectedWeeks(4); // Reset selected weeks
  };

  const user = userData?.data as CurrentJobSeekerType | CurrentCompanyType | undefined;
  const isLoggedIn = !!currentUser;
  const isJobseeker = user?.role === UserRole.JOBSEEKER;
  const isRecruiter = user?.role === UserRole.RECRUITER;

  console.log("[Header] User State:", {
    userData: userData?.data,
    currentUser,
    user,
    isLoggedIn,
    isJobseeker,
    isRecruiter,
    userRole: user?.role,
  });

  // Common navigation links for all users
  const commonLinks = [
    { href: "/about-us", label: "About Us" },
    { href: "/how-it-works", label: "How It Works" },
    { href: "/contact", label: "Contact" },
  ];

  // Role-specific navigation links
  const roleSpecificLinks = [
    ...((!isLoggedIn || isJobseeker) && !isRecruiter
      ? [
          { href: "/for-candidates", label: "For Candidates" },
          { href: "/all-companies", label: "All Companies" },
        ]
      : []),
    ...((!isLoggedIn || isRecruiter) && !isJobseeker
      ? [{ href: "/for-recruiters", label: "For Recruiters" }]
      : []),
  ];

  // Dashboard navigation links when in dashboard context
  const dashboardLinks = [
    {
      href: isJobseeker ? "/dashboard/applied-jobs" : "/company-dashboard/all-jobs",
      label: "Jobs",
    },
    ...commonLinks,
  ];

  const mainNavLinks = dashboardPages
    ? [...dashboardLinks, ...roleSpecificLinks]
    : [...roleSpecificLinks, ...commonLinks];
  console.log({ mainNavLinks });
  const endsAt = user?.proMembershipEndsAt || user?.proTrialEndsAt;
  const isProMember = endsAt ? new Date(endsAt) > new Date() : false;

  // console.log(isProMember, "isProMemberisProMemberisProMember");
  return (
    <>
      <>
        {adminSettingsData?.data.TrialEndsAt &&
          new Date(adminSettingsData.data.TrialEndsAt) > new Date() &&
          userData?.data.role === "RECRUITER" && (
            <>
              <div className="relative overflow-hidden bg-orange-100 text-white border-b border-yellow-300 text-center">
                🚀 Trial running! <span className=" font-semibold">Feature your job</span> to get 3x
                more exposure.
              </div>
            </>
          )}
        {adminSettingsData?.data.TrialEndsAt &&
          new Date(adminSettingsData.data.TrialEndsAt) > new Date() &&
          userData?.data.role === "JOBSEEKER" &&
          !isProMember && (
            <div className="relative overflow-hidden bg-orange-100 text-white border-b border-yellow-300 text-center">
              🎉 Trial’s on! Feature your profile and get noticed faster.
            </div>
          )}
      </>

      <header className="sticky top-0 z-50 w-full bg-white">
        <DesktopHeader
          mainNavLinks={mainNavLinks}
          user={user}
          isProMember={isProMember}
          handlePurchasePro={handlePurchasePro}
          isPurchasingPro={isPurchasingPro}
          unreadMessageCount={totalUnreadCount}
          shouldShowMessageCount={shouldShowCount}
          isNotificationDropdownOpen={isNotificationDropdownOpen}
          setIsNotificationDropdownOpen={setIsNotificationDropdownOpen}
          notifications={notifications}
          unreadCount={unreadCount}
          handleMarkAllAsRead={handleMarkAllAsRead}
          handleNotificationClick={handleNotificationClick}
          handleLogout={handleLogout}
          isJobseeker={isJobseeker}
          isRecruiter={isRecruiter}
          isLoggedIn={isLoggedIn}
        />
        <MobileHeader
          isDrawerOpen={isDrawerOpen}
          setIsDrawerOpen={setIsDrawerOpen}
          mainNavLinks={mainNavLinks}
          user={user}
          unreadMessageCount={totalUnreadCount}
          shouldShowMessageCount={shouldShowCount}
          handleLogout={handleLogout}
          isJobseeker={isJobseeker}
          isRecruiter={isRecruiter}
          isLoggedIn={isLoggedIn}
          handleMarkAllAsRead={handleMarkAllAsRead}
          handleNotificationClick={handleNotificationClick}
          notifications={notifications}
          unreadCount={unreadCount}
        />
      </header>

      <Dialog open={isPurchaseProModalOpen} onOpenChange={setIsPurchaseProModalOpen}>
        <DialogContent className="w-full max-w-2xl p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">
              Become a Pro Member and unlock exclusive features!
            </DialogTitle>
            <DialogDescription className="text-gray-700">
              Current price per week: ${proMemberPricePerWeek}
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 pt-0">
            <div className="mb-6">
              <label htmlFor="weeks" className="block text-sm font-medium text-gray-700 mb-2">
                Select duration:
              </label>
              <select
                id="weeks"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base sm:text-sm rounded-md border-orange-100 border text-orange-100 h-[44px] cursor-pointer"
                value={selectedWeeks}
                onChange={(e) => setSelectedWeeks(Number(e.target.value))}
              >
                <option value={1}>1 Week</option>
                <option value={2}>2 Weeks</option>
                <option value={3}>3 Weeks</option>
                <option value={4}>4 Weeks</option>
              </select>
            </div>
            <p className="text-lg font-bold mb-6">Total Price: ${purchaseProPrice}</p>
          </div>
          <DialogFooter className="p-6 pt-0 flex justify-end gap-4">
            <button
              onClick={cancelPurchasePro}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={confirmPurchasePro}
              className="px-4 py-2 bg-orange-100 text-white rounded-md hover:bg-orange-200"
              disabled={isPurchasingPro}
            >
              {isPurchasingPro ? "Processing..." : "Confirm Purchase"}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Header;
