import { Loader2 } from "lucide-react";
import React from "react";
import { XCircleIcon } from "../Icons";

interface ICVShowCard {
  docName: string;
  date: string;
  onDelete: () => void;
  isDeleting: boolean;
  isActive: boolean;
  isActivating?: boolean;
  onToggleActive: () => void;
}

const CVShowCard = ({
  docName,
  date,
  onDelete,
  isDeleting,
  isActive,
  isActivating = false,
  onToggleActive,
}: ICVShowCard) => {
  return (
    <div
      className={`border p-6 flex flex-col gap-y-3 sm:gap-y-0 rounded-2xl sm:flex-row justify-between sm:items-center sm:self-stretch sm:rounded-full transition-all duration-200 ${
        isActive
          ? "border-green-400 bg-green-50 shadow-md ring-2 ring-green-200"
          : "border-gray-300 bg-white hover:border-gray-400"
      }`}
    >
      <div className="flex items-center gap-3">
        {/* Active indicator dot */}
        <div
          className={`w-3 h-3 rounded-full flex-shrink-0 ${
            isActive ? "bg-green-500 shadow-sm" : "bg-gray-300"
          }`}
        />
        <div>
          <p
            className={`text-base font-medium flex items-center gap-2 ${
              isActive ? "text-green-800" : "text-orange-100"
            }`}
          >
            {docName}
            {isActive && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                ACTIVE CV
              </span>
            )}
          </p>
        </div>
      </div>
      <div>
        <p className={`font-medium text-base ${isActive ? "text-green-700" : "text-gray-100"}`}>
          Uploaded on: {date}
        </p>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span
            className={`text-sm font-medium flex items-center gap-1 ${
              isActive ? "text-green-600" : "text-gray-500"
            }`}
          >
            {isActivating ? <Loader2 className="h-3 w-3 animate-spin" /> : null}
            {isActive ? "Active" : "Inactive"}
          </span>
          {!isActive && (
            <button
              onClick={onToggleActive}
              disabled={isActivating}
              className={`px-3 py-1 rounded-md text-sm font-medium flex items-center gap-1 transition-colors
                ${isActivating ? "bg-gray-200 text-gray-500 cursor-not-allowed" : ""}
                bg-green-100 hover:bg-green-200 text-green-700 border border-green-200`}
            >
              {isActivating && <Loader2 className="h-3 w-3 animate-spin" />}
              {isActivating ? "Activating..." : "Activate"}
            </button>
          )}
        </div>
        <button
          onClick={onDelete}
          disabled={isDeleting}
          className="text-red-500 hover:text-red-700 transition-colors flex items-center gap-1"
          aria-label="Delete CV"
        >
          {isDeleting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Deleting...</span>
            </>
          ) : (
            <XCircleIcon />
          )}
        </button>
      </div>
    </div>
  );
};

export default CVShowCard;
