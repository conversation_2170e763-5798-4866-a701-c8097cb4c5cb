"use client";

import { Suspense, use } from "react";
import CandidateCard from "@/components/Cards/CandidateCard";
import CandidateDetail from "@/components/Cards/CandidateDetail";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetProfileBySlug, useGetRelatedCandidates } from "@/hooks/useQuery";
import { IRelatedCandidate } from "@/types/query.types";

// Related Candidates Component
const RelatedCandidates = ({ jobSeekerProfileId }: { jobSeekerProfileId: string }) => {
  const { data, isLoading, isError } = useGetRelatedCandidates(jobSeekerProfileId, {
    page: 1,
    limit: 3,
    includeSkills: true,
    includeJobType: true,
    includeJobCategory: true,
    includeSalaryRange: true,
    includeLocation: true,
    maxDistance: 50,
  });

  if (isLoading) {
    return (
      <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-5">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="p-6 border border-gray-300 rounded-2xl shadow-md h-[400px] animate-pulse bg-gray-100"
          ></div>
        ))}
      </div>
    );
  }

  if (isError) {
    return <div className="text-center py-8">Error loading related candidates.</div>;
  }

  const relatedCandidates = data?.data?.data || [];

  if (relatedCandidates.length === 0) {
    return <div className="text-center py-8">No related candidates found.</div>;
  }

  // Helper function to format salary range
  const formatSalaryRange = (candidate: IRelatedCandidate) => {
    if (!candidate.jobPreferences.salaryRangeStart && !candidate.jobPreferences.salaryRangeEnd) {
      return "Not specified";
    }

    // Format numbers without using toLocaleString to ensure server/client consistency
    const formatNumber = (num: number) => {
      return `$${num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
    };

    const start = candidate.jobPreferences.salaryRangeStart
      ? formatNumber(candidate.jobPreferences.salaryRangeStart)
      : "";

    const end = candidate.jobPreferences.salaryRangeEnd
      ? formatNumber(candidate.jobPreferences.salaryRangeEnd)
      : "";

    return start && end ? `${start} - ${end}` : start || end;
  };

  return (
    <>
      {relatedCandidates.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-black-100 mb-6 mt-14">Related Candidates</h2>
          <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-5">
            {relatedCandidates.map((candidate) => (
              <div key={candidate._id}>
                <CandidateCard
                  candidateId={candidate._id}
                  candidateDescription={candidate.userProfile.shortBio || "No bio provided."}
                  candidateImage={candidate.userProfile.profilePicture || DEFAULT_IMAGE}
                  candidateLocation={candidate.jobPreferences.location?.city || ""}
                  candidateName={`${candidate.userProfile.firstName} ${candidate.userProfile.lastName}`}
                  candidateProfessional={candidate.userProfile.designation || "Not specified"}
                  candidateSalary={formatSalaryRange(candidate)}
                  candidateSkills={candidate.skills}
                  link={`/for-recruiters/${candidate.userProfile.slug || candidate._id}`}
                  proMembershipEndsAt={candidate.userProfile.proMembershipEndsAt}
                  proTrialEndsAt={candidate.userProfile.proTrialEndsAt}
                  isSaved={candidate.isSaved}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
};

export default function CandidateDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = use(params);
  const { data, isLoading, isError } = useGetProfileBySlug(slug);

  const profile = data?.data;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
      </div>
    );
  }

  if (isError || !profile) {
    return (
      <div className="flex justify-center items-center min-h-[400px] py-20">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-500 mb-4">Error Loading Profile</h2>
          <p className="text-gray-600">
            Unable to load the candidate profile. Please try again later.
          </p>
        </div>
      </div>
    );
  }

  // Format salary range
  const salaryRange =
    profile.jobPreferences &&
    profile.jobPreferences.salaryRangeStart &&
    profile.jobPreferences.salaryRangeEnd
      ? `$${profile.jobPreferences.salaryRangeStart} - $${profile.jobPreferences.salaryRangeEnd}`
      : "Not specified";

  // Get job category as string
  const jobCategory = profile.jobPreferences?.jobCategory?.join(", ") || "Not specified";

  // Note: The profile contains additional data like experiences, academicExperiences, and certificates
  // that could be used to enhance the UI in future iterations

  return (
    <>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="lg:p-[30px]">
            <Suspense
              fallback={<div className="animate-pulse h-[400px] bg-gray-100 rounded-lg"></div>}
            >
              <CandidateDetail
                candidateId={profile._id}
                category={jobCategory}
                cityName={profile.userProfile.location?.formattedAddress || "Not specified"}
                imageUrl={profile.userProfile.profilePicture || DEFAULT_IMAGE}
                jobTitle={`${profile.userProfile.firstName} ${profile.userProfile.lastName}`}
                jobType={profile.jobPreferences?.jobType || "Not specified"}
                salaryRange={salaryRange}
                designation={profile.userProfile?.designation || "Not specified"}
                bioDescription={profile.userProfile.shortBio || "No bio provided."}
                skillsExperience={profile.skills || []}
                skillsTags={profile.skills || []}
                location={profile.userProfile.location?.formattedAddress || "Not specified"}
                expectedSalary={salaryRange}
                experience={"Not specified"} // This information might not be directly available
                qualification={"Not specified"} // This information might not be directly available
                email={profile.user?.email || "Not specified"}
                socialNetworks={profile?.socialNetworks}
                // Pass dynamic data for education, work experience, and achievements
                academicExperiences={profile.academicExperiences || []}
                workExperiences={
                  profile.experiences?.map((exp) => ({
                    ...exp,
                    endDate: exp.endDate || "",
                  })) || []
                }
                achievements={profile.achievements || []}
                certificates={profile.certificates || []}
                downLoadCvUrl={profile.cvAttachments?.[0]?.cvUrl || "#"}
                isAllCompanies={false}
                proMembershipEndsAt={profile.proMembershipEndsAt}
                proTrialEndsAt={profile.proTrialEndsAt}
                isSaved={profile.isSaved}
              />
            </Suspense>
            <div>
              <Suspense
                fallback={
                  <div className="grid lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-5">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div
                        key={index}
                        className="p-6 border border-gray-300 rounded-2xl shadow-md h-[400px] animate-pulse bg-gray-100"
                      ></div>
                    ))}
                  </div>
                }
              >
                <RelatedCandidates jobSeekerProfileId={profile._id} />
              </Suspense>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
