"use client";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useForm, type SubmitH<PERSON><PERSON>, Controller, useWatch } from "react-hook-form";
import { toast } from "sonner";
import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { SearchableCategorySelect } from "@/components/ui/searchable-category-select";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile, useGetAllEnums } from "@/hooks/useQuery";
import type { IJobPreferences } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

// Form interface with jobCategory as string for UI handling
interface IJobPreferencesForm extends Omit<IJobPreferences, "jobCategory"> {
  jobCategory: string;
}

const JobPreferences = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: profileData } = useGetJobSeekerProfile();
  const { data: enumsData, isLoading: isEnumsLoading, error: enumsError } = useGetAllEnums();

  const [coordinates, setCoordinates] = useState({ lat: 0, lng: 0 });
  const [formattedAddress, setFormattedAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [country, setCountry] = useState("");
  const [isLocationSelected, setIsLocationSelected] = useState(false);
  const [formHasData, setFormHasData] = useState(false);

  const {
    ready,
    value,
    setValue,
    suggestions: { status, data },
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      componentRestrictions: { country: "au" },
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
      router.push("/profile-completed");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const [jobCategoryError, setJobCategoryError] = useState<string | null>(null);
  const [jobTypeError, setJobTypeError] = useState<string | null>(null);
  const [locationError, setLocationError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue: setFormValue,
    control,
    formState: { errors },
  } = useForm<IJobPreferencesForm>({
    defaultValues: {
      jobCategory: profileData?.data.jobPreferences?.jobCategory?.[0] || "",
      jobType: profileData?.data.jobPreferences?.jobType || "",
      salaryRangeStart: profileData?.data.jobPreferences?.salaryRangeStart || 0,
      salaryRangeEnd: profileData?.data.jobPreferences?.salaryRangeEnd || 0,
      location: {
        type: "Point",
        coordinates: [
          profileData?.data.jobPreferences?.location?.coordinates?.[0] || 0,
          profileData?.data.jobPreferences?.location?.coordinates?.[1] || 0,
        ],
        formattedAddress: profileData?.data.jobPreferences?.location?.formattedAddress || "",
        city: profileData?.data.jobPreferences?.location?.city || "",
        state: profileData?.data.jobPreferences?.location?.state || "",
        country: profileData?.data.jobPreferences?.location?.country || "",
      },
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  // Initialize location data if exists
  React.useEffect(() => {
    if (profileData?.data.jobPreferences?.location) {
      const locationData = profileData.data.jobPreferences.location;
      if (
        locationData.formattedAddress &&
        locationData.coordinates?.[0] !== 0 &&
        locationData.coordinates?.[1] !== 0
      ) {
        setValue(locationData.formattedAddress, false);
        setCoordinates({ lat: locationData.coordinates[1], lng: locationData.coordinates[0] });
        setFormattedAddress(locationData.formattedAddress);
        setCity(locationData.city || "");
        setState(locationData.state || "");
        setCountry(locationData.country || "");
        setIsLocationSelected(true);
      }
    }
  }, [profileData, setValue]);

  const handleSelect = async (address: string) => {
    setValue(address, false);
    clearSuggestions();

    try {
      const results = await getGeocode({ address });
      const { lat, lng } = getLatLng(results[0]);
      const addressComponents = results[0].address_components;

      // Define a type for address components
      interface AddressComponent {
        long_name: string;
        short_name: string;
        types: string[];
      }

      const city =
        addressComponents.find((c: AddressComponent) => c.types.includes("locality"))?.long_name ||
        "";
      const state =
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_1")
        )?.long_name || "";
      const country =
        addressComponents.find((c: AddressComponent) => c.types.includes("country"))?.long_name ||
        "";

      setCoordinates({ lat, lng });
      setFormattedAddress(address);
      setCity(city);
      setState(state);
      setCountry(country);

      setFormValue("location", {
        type: "Point",
        coordinates: [lat, lng],
        formattedAddress: address,
        city,
        state,
        country,
      });

      // Mark location as selected from suggestions
      setIsLocationSelected(true);
      setLocationError(null); // Clear error when valid location is selected
    } catch {
      toast.error("Failed to get location details");
    }
  };

  // Handle skip functionality
  const handleSkip = () => {
    router.push("/profile-completed");
  };

  // Check if form has any data
  const hasAnyData = () => {
    const formData = control._formValues;
    return !!(
      formData.jobCategory ||
      formData.jobType ||
      (formData.salaryRangeStart && formData.salaryRangeStart > 0) ||
      (formData.salaryRangeEnd && formData.salaryRangeEnd > 0) ||
      (formattedAddress && isLocationSelected)
    );
  };

  // Watch form values to trigger re-render when data changes
  const watchedFormValues = useWatch({ control });

  // Update formHasData state when form values change
  React.useEffect(() => {
    setFormHasData(hasAnyData());
  }, [watchedFormValues, formattedAddress, isLocationSelected]);

  const onSubmit: SubmitHandler<IJobPreferencesForm> = async (data) => {
    // If any data is filled, validate all fields are required
    let hasError = false;

    if (!data.jobCategory) {
      setJobCategoryError("Job Category is required");
      hasError = true;
    }

    if (!data.jobType) {
      setJobTypeError("Job Type is required");
      hasError = true;
    }

    // Additional validation before submission
    if (!data.salaryRangeStart || data.salaryRangeStart <= 0) {
      toast.error("Salary range start is required and must be greater than 0");
      hasError = true;
    }

    if (!data.salaryRangeEnd || data.salaryRangeEnd <= 0) {
      toast.error("Salary range end is required and must be greater than 0");
      hasError = true;
    }

    if (data.salaryRangeStart >= data.salaryRangeEnd) {
      toast.error("End salary must be greater than start salary");
      hasError = true;
    }

    if (data.salaryRangeStart > 1000000 || data.salaryRangeEnd > 1000000) {
      toast.error("Salary cannot exceed 1,000,000");
      hasError = true;
    }

    if (
      !formattedAddress ||
      coordinates.lat === 0 ||
      coordinates.lng === 0 ||
      !isLocationSelected
    ) {
      setLocationError("Please select valid location");
      hasError = true;
    }

    if (hasError) {
      return;
    }

    try {
      updateProfile({
        jobPreferences: {
          ...data,
          jobCategory: [data.jobCategory], // Convert single string to array for API
          location: {
            type: "Point",
            coordinates: [coordinates.lng, coordinates.lat], // Ensure correct order
            formattedAddress,
            city,
            state,
            country,
          },
        },
      });
    } catch {
      toast.error("Failed to save job preferences");
    }
  };

  if (isEnumsLoading) {
    return <div>Loading...</div>;
  }

  if (enumsError) {
    return <div>Error loading enums data. Please try again later.</div>;
  }

  const jobTypes = enumsData?.data.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, value]) => ({
        label: key.replace(/_/g, " "), // Format job type names
        value,
      }))
    : [];

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Job Preferences</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3">Set your Job Alerts</h3>
      <div className="mt-10">
        {/* Job Category */}
        <div className="mb-6">
          <Label htmlFor="jobCategory" className="mb-5 block text-black-100 font-medium">
            Job Category
          </Label>
          <Controller
            name="jobCategory"
            control={control}
            render={({ field: { onChange, value } }) => (
              <SearchableCategorySelect
                value={value || ""}
                onValueChange={(selectedValue) => {
                  onChange(selectedValue);
                  setJobCategoryError(null);
                }}
                placeholder="Select job category"
              />
            )}
          />
          {(jobCategoryError || errors.jobCategory) && (
            <p className="text-red-500 text-sm mt-1">
              {jobCategoryError || errors.jobCategory?.message}
            </p>
          )}
        </div>

        {/* Job Type */}
        <div className="mb-6">
          <Label htmlFor="jobType" className="mb-3 block">
            Select Job Type
          </Label>
          <Controller
            name="jobType"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Select
                onValueChange={(selectedValue) => {
                  onChange(selectedValue);
                  setJobTypeError(null);
                }}
                value={value}
                defaultValue={profileData?.data.jobPreferences?.jobType}
              >
                <SelectTrigger
                  className={`w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base ${jobTypeError || errors.jobType ? "border-red-500 border-2" : ""}`}
                >
                  <SelectValue placeholder="Select a Job Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {jobTypes.map(({ label, value: itemValue }) => (
                      <SelectItem key={itemValue} value={itemValue}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {(jobTypeError || errors.jobType) && (
            <p className="text-red-500 text-sm mt-1">{jobTypeError || errors.jobType?.message}</p>
          )}
        </div>

        {/* Salary Range */}
        <div className="mb-6">
          <Label htmlFor="salaryRangeStart" className="mb-3 block">
            Salary Range Start
          </Label>
          <Input
            id="salaryRangeStart"
            type="number"
            className={inputClasses}
            {...register("salaryRangeStart", {
              valueAsNumber: true,
              min: {
                value: 1,
                message: "Salary must be greater than 0",
              },
              max: {
                value: 1000000,
                message: "Salary cannot exceed 1,000,000",
              },
            })}
          />
          {errors.salaryRangeStart && (
            <p className="text-red-500 text-sm">{errors.salaryRangeStart.message}</p>
          )}
        </div>

        <div className="mb-6">
          <Label htmlFor="salaryRangeEnd" className="mb-3 block">
            Salary Range End
          </Label>
          <Input
            id="salaryRangeEnd"
            type="number"
            className={inputClasses}
            {...register("salaryRangeEnd", {
              valueAsNumber: true,
              min: {
                value: 1,
                message: "Salary must be greater than 0",
              },
              max: {
                value: 1000000,
                message: "Salary cannot exceed 1,000,000",
              },
              validate: (value) => {
                // Get the current value of salaryRangeStart from the form
                const startSalaryInput = document.getElementById(
                  "salaryRangeStart"
                ) as HTMLInputElement;
                const startSalary = startSalaryInput
                  ? Number.parseFloat(startSalaryInput.value)
                  : 0;
                return value > startSalary || "End salary must be greater than start salary";
              },
            })}
          />
          {errors.salaryRangeEnd && (
            <p className="text-red-500 text-sm">{errors.salaryRangeEnd.message}</p>
          )}
        </div>

        {/* Location */}
        <div className="mb-6">
          <Label htmlFor="location" className="mb-3 block">
            Location
          </Label>
          <div className="relative">
            <Input
              className={inputClasses}
              id="location"
              placeholder="e.g., New York, Remote"
              value={value}
              onChange={(e) => {
                setValue(e.target.value);
                setIsLocationSelected(false); // Reset when user types manually
                setLocationError(null); // Clear error when user types
              }}
              disabled={!ready}
            />
            {status === "OK" && (
              <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-2 max-h-60 overflow-y-auto">
                {data.map(({ place_id, description }) => (
                  <li
                    key={place_id}
                    className="p-2 cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSelect(description)}
                  >
                    {description}
                  </li>
                ))}
              </ul>
            )}
          </div>
          {(locationError || errors.location) && (
            <p className="text-red-500 text-sm mt-1">{locationError || errors.location?.message}</p>
          )}
        </div>

        {/* Submit Buttons */}
        <div className="flex gap-5">
          <button
            disabled={isPending}
            onClick={() => router.back()}
            type="button"
            className="font-bold py-4 px-10 text-black font-base rounded-full inline-flex items-center justify-center space-x-2 bg-[#E7E7E7]"
          >
            Go Back
          </button>

          {/* Skip Button - Always visible */}
          <button
            disabled={isPending}
            type="button"
            onClick={handleSkip}
            className="font-bold py-4 px-10 text-black font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-200 hover:bg-gray-300"
          >
            Skip
          </button>

          {/* Save Button - Only visible when form has data */}
          {formHasData && (
            <button
              disabled={isPending}
              type="button"
              onClick={() => handleSubmit(onSubmit)()}
              className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
            >
              {isPending ? "Loading..." : "Save Details"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default JobPreferences;
