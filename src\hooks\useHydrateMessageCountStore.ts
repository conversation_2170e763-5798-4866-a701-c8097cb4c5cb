"use client";

import { useEffect, useState } from "react";
import { useMessageCountStore } from "@/store/useMessageCountStore";

/**
 * Hook to hydrate the message count store from localStorage
 * This ensures the store is properly initialized before using it
 */
export const useHydrateMessageCountStore = () => {
  const [isHydrated, setIsHydrated] = useState(false);
  const { hasHydrated, setHasHydrated } = useMessageCountStore();

  useEffect(() => {
    // Wait for the store to be hydrated from localStorage
    const unsubscribe = useMessageCountStore.persist.onFinishHydration(() => {
      setHasHydrated();
      setIsHydrated(true);
    });

    // If already hydrated, set the state immediately
    if (hasHydrated) {
      setIsHydrated(true);
    }

    return unsubscribe;
  }, [hasHydrated, setHasHydrated]);

  return isHydrated;
};
