"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2, Ref<PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";

import CVShowCard from "@/components/Cards/CVShowCard";
import UploadCVCard from "@/components/Cards/UploadCVCard";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useUploadCV, useDeleteCV, useActivateCV } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { ICvAttachment } from "@/types/query.types";

const CVAttachmentComp = () => {
  const { data, isLoading, isError, refetch } = useGetJobSeekerProfile();
  const { mutateAsync: uploadCV, isPending: isUploading } = useUploadCV();
  const { mutateAsync: deleteCV, isPending: isPendingDeleteCV } = useDeleteCV();
  const { mutateAsync: activateCV, isPending: isPendingActivateCV } = useActivateCV();

  const [cvs, setCvs] = useState<ICvAttachment[]>([]);
  const [deletingCV, setDeletingCV] = useState<string | null>(null); // Track the s3Key of the CV being deleted
  const [activatingCV, setActivatingCV] = useState<string | null>(null); // Track the s3Key of the CV being activated/deactivated

  useEffect(() => {
    if (data?.data?.cvAttachments) {
      // Sort CVs to show active ones first, then by upload date (newest first)
      const sortedCvs = [...data.data.cvAttachments].sort((a, b) => {
        // First, sort by active status (active CVs first)
        if (a.isActive && !b.isActive) return -1;
        if (!a.isActive && b.isActive) return 1;

        // Then sort by upload date (newest first)
        return new Date(b.uploadedDate).getTime() - new Date(a.uploadedDate).getTime();
      });
      setCvs(sortedCvs);
    }
  }, [data]);

  // Track upload progress
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploadError, setIsUploadError] = useState<boolean>(false);

  const handleUploadCV = async (file: File) => {
    try {
      setIsUploadError(false);
      setUploadProgress(0);

      // Create a progress handler
      const handleProgress = (progress: number) => {
        setUploadProgress(progress);
      };

      // Pass the progress handler to the upload function
      await uploadCV({
        file,
        options: { onProgress: handleProgress },
      });

      toast.success("CV uploaded successfully");
      // Reset progress after successful upload
      setUploadProgress(0);
      // Instead of directly adding to state, refetch to ensure data consistency
      refetch();
    } catch (error) {
      setIsUploadError(true);
      setUploadProgress(0);
      toast.error("Failed to upload CV. Please try again.");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Upload error:", error);
      }
    }
  };

  // State for delete confirmation dialog
  const [cvToDelete, setCvToDelete] = useState<string | null>(null);

  // Open delete confirmation dialog
  const openDeleteDialog = (s3Key: string) => {
    setCvToDelete(s3Key);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setCvToDelete(null);
  };

  // Handle CV deletion
  const handleDeleteCV = async (s3Key: string) => {
    setDeletingCV(s3Key); // Set the s3Key of the CV being deleted
    try {
      const response = await deleteCV({ s3Key });
      const updatedCvs = Object.values(response.data).filter((cv: ICvAttachment) => cv.isActive);
      setCvs(updatedCvs); // Update the CV list with active CVs
      toast.success("CV deleted successfully");
    } catch (error) {
      toast.error("Failed to delete CV");
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Delete CV error:", error);
      }
    } finally {
      setDeletingCV(null); // Reset the deleting state
      closeDeleteDialog(); // Close the dialog
    }
  };

  const handleToggleActivateCV = async (s3Key: string, currentStatus: boolean) => {
    setActivatingCV(s3Key); // Set the s3Key of the CV being activated/deactivated
    try {
      await activateCV({ s3Key });

      // Update the local state to reflect the change and re-sort
      setCvs((prevCvs) => {
        const updatedCvs = prevCvs.map((cv) =>
          cv.s3Key === s3Key ? { ...cv, isActive: !cv.isActive } : cv
        );

        // Re-sort after status change to maintain active CVs at top
        return updatedCvs.sort((a, b) => {
          // First, sort by active status (active CVs first)
          if (a.isActive && !b.isActive) return -1;
          if (!a.isActive && b.isActive) return 1;

          // Then sort by upload date (newest first)
          return new Date(b.uploadedDate).getTime() - new Date(a.uploadedDate).getTime();
        });
      });

      // Show appropriate success message
      toast.success(currentStatus ? "CV deactivated successfully" : "CV activated successfully");
    } catch (error) {
      toast.error(
        currentStatus
          ? "Failed to deactivate CV. Please try again."
          : "Failed to activate CV. Please try again."
      );
      if (process.env.NODE_ENV !== "production") {
        // eslint-disable-next-line no-console
        console.error("Activate CV error:", error);
      }
    } finally {
      setActivatingCV(null); // Reset the activating state
    }
  };

  if (isLoading || isUploading || isPendingActivateCV || isPendingDeleteCV) {
    return (
      <div className="space-y-6">
        <h2 className="text-orange-100 text-3xl font-bold mb-10">CV Attachment</h2>
        <div className="mb-10 animate-pulse bg-gray-200 h-[170px] rounded-[20px]"></div>
        {[1, 2].map((item) => (
          <div
            key={item}
            className="border border-gray-300 p-6 rounded-2xl animate-pulse bg-gray-100 h-24"
          ></div>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="space-y-6">
        <h2 className="text-orange-100 text-3xl font-bold mb-10">CV Attachment</h2>
        <div className="p-6 border border-red-200 bg-red-50 rounded-lg flex flex-col items-center justify-center space-y-4">
          <AlertCircle className="h-10 w-10 text-red-500" />
          <p className="text-red-500 font-medium text-lg">Error loading CVs</p>
          <p className="text-gray-600 text-center">
            We couldn&apos;t load your CV attachments. Please try again later.
          </p>
          <Button onClick={() => refetch()} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" /> Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <h2 className="text-orange-100 text-3xl font-bold mb-10">CV Attachment</h2>
      <div className="mb-10">
        <UploadCVCard
          onUpload={handleUploadCV}
          isUploading={isUploading}
          uploadProgress={uploadProgress}
          isError={isUploadError}
        />
      </div>
      <div className="space-y-6">
        {cvs.length === 0 ? (
          <p className="text-gray-500">No CVs uploaded yet. Please upload a CV.</p>
        ) : (
          <>
            {/* CV List Header */}
            <div className="flex items-center justify-between border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-800">Your CVs ({cvs.length})</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  Active: {cvs.filter((cv) => cv.isActive).length}
                </span>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
            </div>

            {/* CV List */}
            {cvs.map((cv: ICvAttachment) => (
              <CVShowCard
                key={cv.s3Key}
                docName={cv.cvName}
                date={new Date(cv.uploadedDate).toLocaleDateString()}
                onDelete={() => openDeleteDialog(cv.s3Key)}
                isDeleting={deletingCV === cv.s3Key} // Only show loading for the CV being deleted
                isActive={cv.isActive}
                isActivating={activatingCV === cv.s3Key} // Only show loading for the CV being activated/deactivated
                onToggleActive={() => handleToggleActivateCV(cv.s3Key, cv.isActive)}
              />
            ))}
          </>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!cvToDelete} onOpenChange={(open) => !open && closeDeleteDialog()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete CV</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this CV? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={closeDeleteDialog}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => cvToDelete && handleDeleteCV(cvToDelete)}
              disabled={!!deletingCV}
              className="flex items-center gap-2"
            >
              {deletingCV ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete CV
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CVAttachmentComp;
