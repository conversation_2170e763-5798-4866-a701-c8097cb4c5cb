"use client";
import DashboardSidebar from "@/components/Sections/DashboardSidebar";
import FullPageLoader from "@/components/ui/FullPageLoader";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetCurrentUser, useGetJobSeekerProfile } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";
import { UserRole } from "@/types/common.types";

const SidebarLayout = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, hasHydrated } = useUserStore();
  const { data: userData } = useGetCurrentUser({
    enabled: !!currentUser && hasHydrated,
  });
  const { data, isLoading } = useGetJobSeekerProfile();
  const skills = data?.data?.skills;

  if (!hasHydrated) {
    return <FullPageLoader message="Loading..." />;
  }

  const isJobSeeker = currentUser?.role === UserRole.JOBSEEKER;

  return (
    <div className="container mx-auto py-14">
      <div className="lg:flex gap-x-10">
        <DashboardSidebar
          candidateDesignation={
            (isJobSeeker &&
              userData?.data &&
              "designation" in userData.data &&
              userData.data.designation) ||
            ""
          }
          candidateName={
            isLoading
              ? "Loading..."
              : `${
                  (isJobSeeker &&
                    userData?.data &&
                    "firstName" in userData.data &&
                    userData.data.firstName) ||
                  ""
                } ${
                  (isJobSeeker &&
                    userData?.data &&
                    "lastName" in userData.data &&
                    userData.data.lastName) ||
                  ""
                }`
          }
          getFeatured={true}
          image={userData?.data?.profilePicture || DEFAULT_IMAGE}
          candidateSkills={isLoading ? ["Loading..."] : skills}
          CandidateDashboard={true}
          CompanyDashboard={false}
          proMembershipEndsAt={
            (isJobSeeker &&
              userData?.data &&
              "proMembershipEndsAt" in userData.data &&
              userData.data.proMembershipEndsAt) ||
            undefined
          }
          proTrialEndsAt={
            (isJobSeeker &&
              userData?.data &&
              "proTrialEndsAt" in userData.data &&
              userData.data.proTrialEndsAt) ||
            undefined
          }
        />
        <div className="border border-gray-300 lg:w-[calc(100%-350px)] rounded-[18px] lg:p-7 p-2">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SidebarLayout;
