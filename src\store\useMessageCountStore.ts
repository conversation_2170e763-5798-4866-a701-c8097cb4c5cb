import { create } from "zustand";
import { persist } from "zustand/middleware";

/**
 * Interface for managing unread message counts
 */
interface MessageCountStore {
  // State
  totalUnreadCount: number;
  isOnMessagePage: boolean;
  shouldShowCount: boolean;
  hasHydrated: boolean;

  // Actions
  setTotalUnreadCount: (count: number) => void;
  incrementUnreadCount: (increment?: number) => void;
  decrementUnreadCount: (decrement?: number) => void;
  setIsOnMessagePage: (isOnPage: boolean) => void;
  setShouldShowCount: (shouldShow: boolean) => void;
  setHasHydrated: () => void;
  resetCount: () => void;
}

/**
 * Message count store implementation using Zustand
 * Manages unread message counts with page visibility logic
 */
export const useMessageCountStore = create<MessageCountStore>()(
  persist(
    (set, get) => ({
      // Initial state
      totalUnreadCount: 0,
      isOnMessagePage: false,
      shouldShowCount: true,
      hasHydrated: false,

      // Actions
      setTotalUnreadCount: (count) => {
        const { isOnMessagePage } = get();
        set({
          totalUnreadCount: Math.max(0, count),
          shouldShowCount: !isOnMessagePage && count > 0,
        });
      },

      incrementUnreadCount: (increment = 1) => {
        const { totalUnreadCount, isOnMessagePage } = get();
        const newCount = totalUnreadCount + increment;
        set({
          totalUnreadCount: Math.max(0, newCount),
          shouldShowCount: !isOnMessagePage && newCount > 0,
        });
      },

      decrementUnreadCount: (decrement = 1) => {
        const { totalUnreadCount, isOnMessagePage } = get();
        const newCount = totalUnreadCount - decrement;
        set({
          totalUnreadCount: Math.max(0, newCount),
          shouldShowCount: !isOnMessagePage && newCount > 0,
        });
      },

      setIsOnMessagePage: (isOnPage) => {
        const { totalUnreadCount } = get();
        set({
          isOnMessagePage: isOnPage,
          shouldShowCount: !isOnPage && totalUnreadCount > 0,
        });
      },

      setShouldShowCount: (shouldShow) => {
        set({ shouldShowCount: shouldShow });
      },

      setHasHydrated: () => {
        set({ hasHydrated: true });
      },

      resetCount: () => {
        set({
          totalUnreadCount: 0,
          shouldShowCount: false,
        });
      },
    }),
    {
      name: "message-count-store",
      partialize: (state) => ({
        totalUnreadCount: state.totalUnreadCount,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.setHasHydrated();
        }
      },
    }
  )
);
