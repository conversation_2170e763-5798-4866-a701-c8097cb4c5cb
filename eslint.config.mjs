import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import pluginUnusedImports from "eslint-plugin-unused-imports";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript", "prettier"),
  {
    plugins: {
      "unused-imports": pluginUnusedImports,
    },
    rules: {
      "@typescript-eslint/no-explicit-any": "error", // ❌ No `any`
      "@typescript-eslint/ban-ts-comment": "error", // ❌ No `@ts-ignore`
      "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }], // ❌ No unused vars
      "no-unused-vars": "off", // ✅ Disable default rule (use TS version)
      "no-console": "warn", // ⚠️ Warn on `console.log`

      // ✅ Disable problematic import/order rule
      "import/order": "off",

      "unused-imports/no-unused-imports": "error", // ❌ Auto-remove unused imports
      "unused-imports/no-unused-vars": [
        "error",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_",
        },
      ],
    },
  },
];

export default eslintConfig;
