import React from "react";
import { AlertTriangle } from "lucide-react";

interface RestrictedAccessProps {
  message?: string;
  className?: string;
}

const RestrictedAccess: React.FC<RestrictedAccessProps> = ({
  message = "Your account is restricted by admin. Contact admin for more details.",
  className = "",
}) => {
  return (
    <div
      className={`flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg ${className}`}
    >
      <AlertTriangle className="w-16 h-16 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-red-800 mb-2">Access Restricted</h3>
      <p className="text-red-600 text-center max-w-md">{message}</p>
    </div>
  );
};

export default RestrictedAccess;
