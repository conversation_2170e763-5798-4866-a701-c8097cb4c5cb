import React from "react";
import { useRestriction } from "@/hooks/useRestriction";
import RestrictedAccess from "./RestrictedAccess";

interface WithRestrictionCheckOptions {
  checkFunction: (restrictions: ReturnType<typeof useRestriction>) => boolean;
  message?: string;
  fallbackComponent?: React.ComponentType;
}

export function withRestrictionCheck<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithRestrictionCheckOptions
) {
  const WithRestrictionCheckComponent = (props: P) => {
    const restrictions = useRestriction();
    const canAccess = options.checkFunction(restrictions);

    if (!canAccess) {
      if (options.fallbackComponent) {
        const FallbackComponent = options.fallbackComponent;
        return <FallbackComponent />;
      }
      return <RestrictedAccess message={options.message} />;
    }

    return <WrappedComponent {...props} />;
  };

  WithRestrictionCheckComponent.displayName = `withRestrictionCheck(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithRestrictionCheckComponent;
}
