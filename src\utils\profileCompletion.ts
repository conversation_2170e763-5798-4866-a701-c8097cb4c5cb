import { Ilocation, IProfileData } from "@/types/query.types";

/**
 * Calculates the profile completion percentage based on the jobseeker profile data
 * @param profileData - The jobseeker profile data
 * @returns The completion percentage (0-100)
 */
export function calculateProfileCompletion(profileData: IProfileData | undefined): number {
  if (!profileData) return 0;

  let completedFields = 0;
  let totalFields = 0;

  // User Profile fields (weight: 30%)
  const userProfileFields = [
    "firstName",
    "lastName",
    "phoneNo",
    "dob",
    "location",
    "shortBio",
    "profilePicture",
    "designation",
  ];

  userProfileFields.forEach((field) => {
    totalFields++;
    const value = profileData.userProfile?.[field as keyof typeof profileData.userProfile];
    if (value && value !== "" && value !== null && value !== undefined) {
      // Special check for location object
      if (field === "location" && typeof value === "object") {
        const location = value as Ilocation;
        if (location.formattedAddress && location.city && location.country) {
          completedFields++;
        }
      } else {
        completedFields++;
      }
    }
  });

  // Skills (weight: 10%)
  totalFields++;
  if (profileData.skills && profileData.skills.length > 0) {
    completedFields++;
  }

  // Job Preferences (weight: 15%)
  const jobPrefFields = [
    "jobType",
    "jobCategory",
    "salaryRangeStart",
    "salaryRangeEnd",
    "location",
  ];
  jobPrefFields.forEach((field) => {
    totalFields++;
    const value = profileData.jobPreferences?.[field as keyof typeof profileData.jobPreferences];
    if (value !== null && value !== undefined && value !== "") {
      if (field === "jobCategory" && Array.isArray(value)) {
        if (value.length > 0) completedFields++;
      } else if (field === "location" && typeof value === "object") {
        const location = value as Ilocation;
        if (location?.formattedAddress && location?.city && location?.country) {
          completedFields++;
        }
      } else if (field === "salaryRangeStart" || field === "salaryRangeEnd") {
        if (typeof value === "number" && value > 0) completedFields++;
      } else {
        completedFields++;
      }
    }
  });

  // Work Experience (weight: 15%)
  totalFields++;
  if (profileData.experiences && profileData.experiences.length > 0) {
    const hasCompleteExperience = profileData.experiences.some(
      (exp) => exp.organizationName && exp.designation && exp.startDate && exp.jobDetails
    );
    if (hasCompleteExperience) completedFields++;
  }

  // Education (weight: 10%)
  totalFields++;
  if (profileData.academicExperiences && profileData.academicExperiences.length > 0) {
    const hasCompleteEducation = profileData.academicExperiences.some(
      (edu) => edu.instituteName && edu.degree && edu.startDate
    );
    if (hasCompleteEducation) completedFields++;
  }

  // Certificates (weight: 5%)
  totalFields++;
  if (profileData.certificates && profileData.certificates.length > 0) {
    const hasCompleteCertificate = profileData.certificates.some(
      (cert) => cert.instituteName && cert.certificate && cert.startDate
    );
    if (hasCompleteCertificate) completedFields++;
  }

  // CV Attachments (weight: 10%)
  totalFields++;
  if (profileData.cvAttachments && profileData.cvAttachments.length > 0) {
    const hasActiveCv = profileData.cvAttachments.some((cv) => cv.isActive && cv.cvUrl);
    if (hasActiveCv) completedFields++;
  }

  // Portfolio Images (weight: 5%)
  totalFields++;
  if (profileData.portFolioImages && profileData.portFolioImages.length > 0) {
    completedFields++;
  }

  // Calculate percentage
  const percentage = Math.round((completedFields / totalFields) * 100);
  return Math.min(percentage, 100); // Ensure it doesn't exceed 100%
}

/**
 * Gets profile completion details with missing fields
 * @param profileData - The jobseeker profile data
 * @returns Object with completion percentage and missing fields
 */
export function getProfileCompletionDetails(profileData: IProfileData | undefined) {
  if (!profileData) {
    return {
      percentage: 0,
      missingFields: ["Complete profile setup required"],
    };
  }

  const missingFields: string[] = [];

  // Check user profile fields
  if (!profileData.userProfile?.firstName) missingFields.push("First Name");
  if (!profileData.userProfile?.lastName) missingFields.push("Last Name");
  if (!profileData.userProfile?.phoneNo) missingFields.push("Phone Number");
  if (!profileData.userProfile?.dob) missingFields.push("Date of Birth");
  if (!profileData.userProfile?.shortBio) missingFields.push("Short Bio");
  if (!profileData.userProfile?.profilePicture) missingFields.push("Profile Picture");
  if (!profileData.userProfile?.designation) missingFields.push("Designation");

  // Check location
  const userLocation = profileData.userProfile?.location;
  if (!userLocation?.formattedAddress || !userLocation?.city || !userLocation?.country) {
    missingFields.push("Location");
  }

  // Check skills
  if (!profileData.skills || profileData.skills.length === 0) {
    missingFields.push("Skills");
  }

  // Check job preferences
  if (!profileData.jobPreferences?.jobType) missingFields.push("Job Type Preference");
  if (
    !profileData.jobPreferences?.jobCategory ||
    profileData.jobPreferences.jobCategory.length === 0
  ) {
    missingFields.push("Job Category Preference");
  }
  if (
    !profileData.jobPreferences?.salaryRangeStart ||
    profileData.jobPreferences.salaryRangeStart <= 0
  ) {
    missingFields.push("Salary Range");
  }

  // Check work experience
  if (!profileData.experiences || profileData.experiences.length === 0) {
    missingFields.push("Work Experience");
  } else {
    const hasCompleteExperience = profileData.experiences.some(
      (exp) => exp.organizationName && exp.designation && exp.startDate && exp.jobDetails
    );
    if (!hasCompleteExperience) {
      missingFields.push("Complete Work Experience Details");
    }
  }

  // Check education
  if (!profileData.academicExperiences || profileData.academicExperiences.length === 0) {
    missingFields.push("Education");
  }

  // Check CV
  if (!profileData.cvAttachments || profileData.cvAttachments.length === 0) {
    missingFields.push("CV/Resume");
  } else {
    const hasActiveCv = profileData.cvAttachments.some((cv) => cv.isActive && cv.cvUrl);
    if (!hasActiveCv) {
      missingFields.push("Active CV/Resume");
    }
  }

  return {
    percentage: calculateProfileCompletion(profileData),
    missingFields,
  };
}
