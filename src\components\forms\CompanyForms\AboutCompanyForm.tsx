"use client";

import { useQueryClient } from "@tanstack/react-query";
import { PlusIcon, TrashIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { useForm, type SubmitH<PERSON><PERSON>, Controller } from "react-hook-form";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
// import { DEFAULT_VIDEO } from "@/constants/app.constants";
import {
  useUpdateCompanyProfile,
  useUploadCompanyVideo,
  useDeleteVideo,
} from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { ICompany } from "@/types/query.types";

const AboutCompanyForm = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  const { mutateAsync: uploadVideo, isPending: isVideoUploadPending } = useUploadCompanyVideo();

  const { mutate: deleteVideo, isPending: isDeletingVideo } = useDeleteVideo();

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    trigger,
  } = useForm<ICompany["aboutCompany"]>({
    defaultValues: {
      description: "",
      companyVideo: {
        s3Key: "",
        url: "",
        uploadedAt: new Date().toISOString(),
      },
    },
    mode: "onChange",
  });

  const companyVideo = watch("companyVideo");
  const description = watch("description");

  // Initialize form with company data
  useEffect(() => {
    if (companyProfile?.aboutCompany) {
      reset({
        description: companyProfile.aboutCompany.description || "",
        companyVideo: companyProfile.aboutCompany.companyVideo,
      });
    }
  }, [companyProfile, reset]);

  const { mutate: updateProfile, isPending: isUpdatePending } = useUpdateCompanyProfile({
    onSuccess: () => {
      toast.success("Company profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
      router.push("/company-profile-completion?stepId=social-networks");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update company profile");
    },
  });

  const handleVideoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validVideoTypes = [
      "video/mp4",
      "video/webm",
      "video/ogg",
      "video/avi",
      "video/mpeg",
      "video/quicktime",
      "video/x-msvideo", // for .avi
      "video/x-matroska", // for .mkv
      "video/3gpp", // .3gp
      "video/3gpp2", // .3g2
    ];

    if (file.size > 5 * 1024 * 1024) {
      toast.error("Video size should be less than 5MB");
      return;
    }

    if (!file.type.startsWith("video/") && !validVideoTypes.includes(file.type)) {
      toast.error("Please upload a valid video file format");
      return;
    }

    try {
      const response = await uploadVideo(file);
      setValue("companyVideo", response.data.companyVideo);
      trigger("companyVideo");
      toast.success("Company video uploaded successfully");
    } catch (error) {
      toast.error((error as Error).message || "Failed to upload company video");
    } finally {
      e.target.value = "";
    }
  };

  const handleDeleteVideo = () => {
    if (!companyVideo?.s3Key) {
      setValue("companyVideo", {
        url: "",
        s3Key: "",
      });
      return;
    }

    deleteVideo(
      { s3Key: companyVideo.s3Key },
      {
        onSuccess: () => {
          setValue("companyVideo", {
            s3Key: "",
            url: "",
          });
          trigger("companyVideo"); // Trigger validation after delete
          toast.success("Video deleted successfully");
        },
        onError: () => {
          toast.error("Failed to delete video");
        },
      }
    );
  };

  const onSubmit: SubmitHandler<ICompany["aboutCompany"]> = async (formData) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const navigateNext = () => router.push("/company-profile-completion?stepId=social-networks");

    // Always update profile on next step button click
    updateProfile(
      {
        aboutCompany: {
          companyVideo: formData.companyVideo || {
            url: "",
            s3Key: "",
            uploadedAt: new Date().toISOString(),
          },
          description: formData.description || "",
        },
      },
      {
        onSuccess: () => {
          toast.success("Company profile updated successfully");
          queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
          navigateNext();
        },
        onError: (error) => {
          toast.error(error.response?.data?.message || "Failed to update company profile");
        },
      }
    );
  };

  return (
    <div className="max-w-4xl mx-auto px-4">
      <h2 className="text-blue-600 text-2xl font-bold">Tell us about Company</h2>
      <h3 className="text-3xl font-semibold text-gray-800 mt-2 mb-6">About Company</h3>

      <form onSubmit={handleSubmit(onSubmit)} className="mt-8">
        <div className="mb-6">
          <Label htmlFor="description" className="mb-2 block text-gray-700">
            Company Description
          </Label>
          <Textarea
            className="min-h-[120px] px-4 py-3 rounded-xl border border-gray-300 focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
            id="description"
            {...register("description", {
              maxLength: {
                value: 500,
                message: "Description should be less than 500 characters ",
              },
            })}
          />
          {(description?.trim() || companyProfile?.aboutCompany?.description?.trim()) && (
            <p className="text-gray-500 text-sm mt-1">500 word limit</p>
          )}
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
          )}
        </div>

        <div className="mb-8">
          <Label htmlFor="companyVideo" className="mb-2 block text-gray-700">
            Company Video
          </Label>

          <Controller
            name="companyVideo"
            control={control}
            // rules={{
            //   validate: (value) => (value?.url ? true : "Company video is required"),
            // }}
            render={({ field }) => (
              <div className="relative">
                <div
                  className={`flex h-[200px] justify-center items-center border-2 rounded-xl ${
                    field.value?.url ? "border-transparent" : "border-gray-300 border-dashed"
                  } bg-gray-50 overflow-hidden`}
                >
                  {isVideoUploadPending || isDeletingVideo ? (
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-orange-500"></div>
                    </div>
                  ) : field.value?.url ? (
                    <div className="relative w-full h-full group">
                      <video
                        src={field.value.url}
                        controls
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={handleDeleteVideo}
                        disabled={isDeletingVideo}
                        className="absolute top-3 right-3 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 disabled:bg-red-400 transition-colors"
                        aria-label="Delete video"
                      >
                        {isDeletingVideo ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                        ) : (
                          <TrashIcon className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  ) : (
                    <label className="flex flex-col items-center justify-center w-full h-full cursor-pointer p-4 text-center">
                      <PlusIcon className="w-10 h-10 text-gray-400 mb-3" />
                      <p className="text-gray-500 font-medium">Upload company video</p>
                      <p className="text-sm text-gray-400 mt-1">.mp4 files less than 5MB</p>
                    </label>
                  )}
                </div>

                {!field.value?.url && !isVideoUploadPending && !isDeletingVideo && (
                  <input
                    type="file"
                    id="companyVideo"
                    accept="video/mp4,video/webm,video/ogg,video/quicktime,video/x-msvideo,video/x-matroska,.mp4,.webm,.ogg,.mov,.avi,.mkv"
                    onChange={handleVideoUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                )}
              </div>
            )}
          />
          {/* {errors.companyVideo && (
            <p className="text-red-500 text-sm mt-1">{errors.companyVideo.message}</p>
          )} */}
        </div>

        <div className="flex gap-5 justify-end">
          <button
            onClick={() => router.back()}
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
          >
            Go Back
          </button>
          <button
            type="submit"
            disabled={isVideoUploadPending || isDeletingVideo || isUpdatePending}
            // onClick={handleSubmit(onSubmit)}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-full px-8 py-3 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isUpdatePending ? (
              <span className="flex items-center justify-center gap-2">
                <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></span>
                Processing...
              </span>
            ) : (
              "Next Step"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AboutCompanyForm;
