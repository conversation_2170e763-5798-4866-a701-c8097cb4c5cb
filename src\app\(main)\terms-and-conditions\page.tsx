import TermsAndConditionsContent from "./TermsAndConditionsContent";
import type { Metadata } from "next";
export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - terms and conditions",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};
export default function TermsAndConditions() {
  return <TermsAndConditionsContent />;
}
