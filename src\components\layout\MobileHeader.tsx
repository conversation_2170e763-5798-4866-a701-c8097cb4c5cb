"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { BellIconNotification } from "../Icons";
import { Button } from "../ui/button";
import { NavLink } from "./NavLink";
import { CurrentCompanyType, CurrentJobSeekerType, INotification } from "@/types/query.types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { DEFAULT_IMAGE } from "@/constants/app.constants";

interface MobileHeaderProps {
  isDrawerOpen: boolean;
  setIsDrawerOpen: (open: boolean) => void;
  mainNavLinks: { href: string; label: string }[];
  user: CurrentJobSeekerType | CurrentCompanyType | undefined;
  unreadMessageCount: number;
  shouldShowMessageCount: boolean;
  handleLogout: () => void;
  isJobseeker: boolean;
  isRecruiter: boolean;
  isLoggedIn: boolean;
  unreadCount: number;
  notifications: INotification[];
  handleMarkAllAsRead: () => void;
  handleNotificationClick: (notification: INotification) => void;
}

const MobileHeader = ({
  isDrawerOpen,
  setIsDrawerOpen,
  mainNavLinks,
  user,
  unreadMessageCount,
  shouldShowMessageCount,
  handleLogout,
  isJobseeker,
  isRecruiter,
  isLoggedIn,
  unreadCount,
  notifications,
  handleMarkAllAsRead,
  handleNotificationClick,
}: MobileHeaderProps) => {
  const router = useRouter();
  const [mobileNotificationDropdownOpen, setMobileNotificationDropdownOpen] = useState(false);

  return (
    <div className="container mx-auto lg:hidden">
      <div className="flex  items-center py-3">
        <Link
          href={
            isLoggedIn
              ? isJobseeker
                ? "/dashboard/applied-jobs"
                : "/company-dashboard/all-jobs"
              : "/"
          }
          className="flex items-center space-x-2"
        >
          <Image
            src="/images/logo.png"
            alt="Jobs Logo"
            width={100}
            height={40}
            className="h-10 w-auto"
          />
        </Link>
        {user && (
          <>
            <DropdownMenu
              open={mobileNotificationDropdownOpen}
              onOpenChange={setMobileNotificationDropdownOpen}
            >
              <DropdownMenuTrigger asChild className="ms-auto mr-3">
                <Button
                  variant="default"
                  className="w-[48px] h-[48px] rounded-full px-0 py-0 relative"
                >
                  {unreadCount > 0 && (
                    <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs">
                      {unreadCount > 9 ? "9+" : unreadCount}
                    </span>
                  )}
                  <BellIconNotification />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80 max-h-96 overflow-y-auto">
                {notifications.length > 0 ? (
                  <>
                    <div className="flex items-center justify-between p-2 border-b">
                      <span className="font-semibold text-sm">Notifications</span>
                      {unreadCount > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleMarkAllAsRead}
                          className="text-xs"
                        >
                          Mark all as read
                        </Button>
                      )}
                    </div>
                    {notifications.map((notification) => (
                      <DropdownMenuItem
                        key={notification._id}
                        className={`p-3 cursor-pointer ${!notification.isRead ? "bg-blue-50" : ""}`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm">{notification.title}</span>
                            {!notification.isRead && (
                              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            )}
                          </div>
                          <span className="text-xs text-gray-600">{notification.message}</span>
                          <span className="text-xs text-gray-600">
                            {new Date(notification.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </>
                ) : (
                  <DropdownMenuItem disabled>
                    <span className="text-gray-500">No notifications</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild className="mr-3">
                <Button variant="default" className="w-[48px] h-[48px] rounded-full px-0 py-0">
                  <Image
                    src={user?.profilePicture || DEFAULT_IMAGE}
                    alt="User Profile"
                    width={48}
                    height={48}
                    className="w-[48px] h-[48px] rounded-full object-cover"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {isJobseeker && (
                  <>
                    <DropdownMenuItem onClick={() => router.push("/dashboard/applied-jobs")}>
                      Dashboard
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push("/settings/my-profile")}>
                      Settings
                    </DropdownMenuItem>
                  </>
                )}
                {isRecruiter && (
                  <>
                    <DropdownMenuItem onClick={() => router.push("/company-dashboard/all-jobs")}>
                      Dashboard
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => router.push("/company-settings/company-profile")}
                    >
                      Company Settings
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem onClick={handleLogout}>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
        <div className={`${user ? "lg:hidden" : "lg:hidden ms-auto"}`}>
          <button
            onClick={() => setIsDrawerOpen(!isDrawerOpen)}
            className="text-gray-800 focus:outline-none"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h16m-7 6h7"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div
        className={`fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity ${
          isDrawerOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={() => setIsDrawerOpen(false)}
      ></div>
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform ${
          isDrawerOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <Link href={"/"} className="flex items-center space-x-2">
            <Image
              src="/images/logo.png"
              alt="Jobs Logo"
              width={100}
              height={40}
              className="h-10 w-auto"
            />
          </Link>
          <button
            onClick={() => setIsDrawerOpen(false)}
            className="text-gray-800 focus:outline-none"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
        <nav className="flex flex-col p-4 space-y-4">
          {mainNavLinks.map((link) => (
            <NavLink key={link.href} href={link.href}>
              {link.label}
            </NavLink>
          ))}
          {user ? (
            <>
              {/* <span className="text-gray-800">
                Welcome,{" "}
                {isRecruiter
                  ? (user as CurrentCompanyType).companyName
                  : `${(user as CurrentJobSeekerType).firstName} ${(user as CurrentJobSeekerType).lastName
                  }`}
              </span> */}
              <Link
                href="/message"
                className="flex items-center gap-2 text-orange-100 font-medium py-2"
              >
                {/* <MessageIcon /> */}
                <span>Messages</span>
                {shouldShowMessageCount && unreadMessageCount > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    {unreadMessageCount > 9 ? "9+" : unreadMessageCount}
                  </span>
                )}
              </Link>

              {isJobseeker && (
                <Button
                  variant="outline"
                  className="rounded-full border border-blue-100 text-blue-100 px-5"
                  onClick={() => router.push("/settings/my-profile")}
                >
                  Settings
                </Button>
              )}
              {isRecruiter && (
                <Button
                  variant="outline"
                  className="rounded-full border border-blue-100 text-blue-100 px-5"
                  onClick={() => router.push("/company-settings/company-profile")}
                >
                  Company Settings
                </Button>
              )}
              <Button
                variant="outline"
                className="rounded-full border border-blue-100 text-blue-100 px-5"
                onClick={handleLogout}
              >
                Logout
              </Button>
            </>
          ) : (
            <>
              <Link href="/login">
                <Button
                  variant="outline"
                  className="rounded-full border border-blue-100 text-blue-100 px-5"
                >
                  Login
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button className="rounded-full px-5 bg-orange-100 hover:bg-orange-100 text-white">
                  Sign Up
                </Button>
              </Link>
            </>
          )}
        </nav>
      </div>
    </div>
  );
};

export default MobileHeader;
