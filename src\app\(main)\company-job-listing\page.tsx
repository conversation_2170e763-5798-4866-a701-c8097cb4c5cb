import { Suspense } from "react";
import CompanyJobListingPage from "./CompanyJobListingPage";
import type { Metadata } from "next";
export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "YesJobs - company job listings",
    description:
      "Browse thousands of job openings across tech, marketing, design, and more. Find your next opportunity with easy filters and instant applications.",
  };
};
export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CompanyJobListingPage />
    </Suspense>
  );
}
