{"name": "yes-job", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.70.0", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "eslint-plugin-unused-imports": "^4.1.4", "firebase": "^11.9.0", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "15.1.6", "quill": "^2.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-intersection-observer": "^9.16.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.2", "swiper": "^11.2.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-places-autocomplete": "^4.0.1", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.23.4", "zod": "^3.25.67", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/lodash": "^4.17.16", "@types/node": "^20.17.28", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "eslint": "^9.23.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}}