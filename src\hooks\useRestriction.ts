import { useUserStore } from "@/store/useUserStore";
import { UserRole } from "@/types/common.types";

export const useRestriction = () => {
  const { currentUser } = useUserStore();

  const isRestricted = currentUser?.isRestricted || false;
  const userRole = currentUser?.role;

  // Check if user can perform specific actions based on role and restriction status
  const canApplyToJob = userRole === UserRole.JOBSEEKER && !isRestricted;
  const canSaveJob = userRole === UserRole.JOBSEEKER && !isRestricted;
  const canAccessMessages = !isRestricted;
  const canAccessPremium = userRole === UserRole.JOBSEEKER && !isRestricted;

  const canPostJob = userRole === UserRole.RECRUITER && !isRestricted;
  const canEditJob = userRole === UserRole.RECRUITER && !isRestricted;
  const canDeleteJob = userRole === UserRole.RECRUITER && !isRestricted;
  const canCreateMessage = userRole === UserRole.RECRUITER && !isRestricted;
  const canShortlistApplicant = userRole === UserRole.RECRUITER && !isRestricted;
  const canRejectApplicant = userRole === UserRole.RECRUITER && !isRestricted;

  return {
    isRestricted,
    userRole,
    // Jobseeker permissions
    canApplyToJob,
    canSaveJob,
    canAccessMessages,
    canAccessPremium,
    // Recruiter permissions
    canPostJob,
    canEditJob,
    canDeleteJob,
    canCreateMessage,
    canShortlistApplicant,
    canRejectApplicant,
  };
};
