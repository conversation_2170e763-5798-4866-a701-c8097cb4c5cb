"use client";

import React, { useState, useMemo } from "react";
import DeleteJobModal from "../DeleteJobModal";
import CompanyJobCardtwo from "@/components/Cards/CompanyJobCardtwo";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useDeleteJob, useUpdateJobStatus, useBoostJob } from "@/hooks/useMutation";
import { useGetJobById, useGetAdminSettings } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";
import { IBoostJobResponseDto } from "@/types/mutation.types";

const JobDetail = ({ jobId }: { jobId: string }) => {
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);

  // State for boost job modal
  const [isBoostModalOpen, setIsBoostModalOpen] = useState(false);
  const [jobToBoostId, setJobToBoostId] = useState<string | null>(null);
  const [selectedWeeks, setSelectedWeeks] = useState(1); // Default to 1 week

  const { mutate: deleteJob } = useDeleteJob();
  const { mutate: updateJobStatus, isPending } = useUpdateJobStatus();
  const { mutate: boostJob, isPending: isBoosting } = useBoostJob();

  const { data: jobData, isLoading: isJobLoading } = useGetJobById(jobId || "", {
    enabled: !!jobId,
  });

  // Fetch admin settings for featured job price
  const { data: adminSettingsData } = useGetAdminSettings();
  const featuredJobPricePerWeek = adminSettingsData?.data?.FeaturedJobPricePerWeek || 0;

  // Calculate boost price dynamically
  const boostPrice = useMemo(() => {
    return featuredJobPricePerWeek * selectedWeeks;
  }, [featuredJobPricePerWeek, selectedWeeks]);

  const handleDelete = (jobId: string) => {
    setJobToDelete(jobId); // Open confirmation modal
  };

  const cancelDelete = () => {
    setJobToDelete(null); // Close modal without deleting
  };

  const toggleJobStatus = (jobId: string, isJobActive: boolean) => {
    updateJobStatus({ jobId, isJobActive });
  };

  const confirmDelete = () => {
    if (jobToDelete) {
      deleteJob(jobToDelete, {
        onSuccess: () => {
          setJobToDelete(null); // Close modal after successful deletion
        },
      });
    }
  };

  // Handle boost job button click
  const handleBoostJob = (jobId: string) => {
    setJobToBoostId(jobId);
    setIsBoostModalOpen(true);
  };

  // Confirm boost job
  const confirmBoost = () => {
    if (jobToBoostId) {
      boostJob(
        { jobId: jobToBoostId, weeks: selectedWeeks },
        {
          onSuccess: (data: IBoostJobResponseDto) => {
            setIsBoostModalOpen(false); // Close modal after successful boost
            setJobToBoostId(null);
            setSelectedWeeks(1); // Reset selected weeks
            if (data?.data?.url) {
              window.open(data.data.url, "_blank");
            }
          },
        }
      );
    }
  };

  // Cancel boost job
  const cancelBoost = () => {
    setIsBoostModalOpen(false);
    setJobToBoostId(null);
    setSelectedWeeks(1); // Reset selected weeks
  };

  if (isJobLoading) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Loading job details...</p>
      </div>
    );
  }

  const job = jobData?.data;
  return (
    <>
      <CompanyJobCardtwo
        jobId={job?._id || ""}
        imageUrl={DEFAULT_IMAGE}
        jobTitle={job?.jobTitle}
        companyName={job?.recruiterProfile?.companyName}
        cityName={job?.location?.city}
        salaryRange={`$${job?.salaryRangeStart} - $${job?.salaryRangeEnd}`}
        jobType={job?.jobType}
        jobClosed={!job?.isJobActive}
        handleDelete={() => handleDelete(job?._id || "")}
        toggleJobStatus={() => toggleJobStatus(job?._id || "", !job?.isJobActive)}
        handleBoostJob={handleBoostJob}
        editJobLink={`/company-dashboard/post-job/${job?._id}`}
        viewJobLink={`/company-dashboard/all-jobs/${job?._id}`}
        category={job?.jobCategory}
        dateCreated={formatDate(job?.createdAt || "")}
        expireDate={formatDate(job?.applicationDeadline || "")}
        shortlistedApplicants={job?.shortlistedApplicants || 0}
        totalApplicants={job?.totalApplicants || 0}
        isDetailPage={true}
        isLoading={isPending || isBoosting}
        premiumExpireAt={job?.premiumExpireAt}
      />
      {jobToDelete && <DeleteJobModal cancelDelete={cancelDelete} confirmDelete={confirmDelete} />}

      <Dialog open={isBoostModalOpen} onOpenChange={setIsBoostModalOpen}>
        <DialogContent className="w-full max-w-2xl p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">
              Need better reach for your job post? Boost it today and get noticed instantly!
            </DialogTitle>
            <DialogDescription className="text-gray-700">
              Current price per week: ${featuredJobPricePerWeek}
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 pt-0">
            <div className="mb-6">
              <label htmlFor="weeks" className="block text-sm font-medium text-gray-700 mb-2">
                Select duration:
              </label>
              <select
                id="weeks"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base sm:text-sm rounded-md border-orange-100 border text-orange-100 h-[44px] cursor-pointer"
                value={selectedWeeks}
                onChange={(e) => setSelectedWeeks(Number(e.target.value))}
              >
                <option value={1}>1 Week</option>
                <option value={2}>2 Weeks</option>
                <option value={3}>3 Weeks</option>
                <option value={4}>4 Weeks</option>
              </select>
            </div>
            <p className="text-lg font-bold mb-6">Total Price: ${boostPrice}</p>
          </div>
          <DialogFooter className="p-6 pt-0 flex justify-end gap-4">
            <button
              onClick={cancelBoost}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={confirmBoost}
              className="px-4 py-2 bg-orange-100 text-white rounded-md hover:bg-orange-200"
            >
              Confirm Boost
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default JobDetail;
