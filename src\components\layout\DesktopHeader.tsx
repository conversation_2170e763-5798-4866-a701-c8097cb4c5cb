"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { BellIconNotification, CrownIcon, MessageIcon } from "../Icons";
import { Button } from "../ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { NavLink } from "./NavLink";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { CurrentCompanyType, CurrentJobSeekerType, INotification } from "@/types/query.types";
import { UserRole } from "@/types/common.types";

interface DesktopHeaderProps {
  mainNavLinks: { href: string; label: string }[];
  user: CurrentJobSeekerType | CurrentCompanyType | undefined;
  isProMember: boolean;
  handlePurchasePro: () => void;
  isPurchasingPro: boolean;
  unreadMessageCount: number;
  shouldShowMessageCount: boolean;
  isNotificationDropdownOpen: boolean;
  setIsNotificationDropdownOpen: (open: boolean) => void;
  notifications: INotification[];
  unreadCount: number;
  handleMarkAllAsRead: () => void;
  handleNotificationClick: (notification: INotification) => void;
  handleLogout: () => void;
  isJobseeker: boolean;
  isRecruiter: boolean;
  isLoggedIn: boolean;
}

const DesktopHeader = ({
  mainNavLinks,
  user,
  isProMember,
  handlePurchasePro,
  isPurchasingPro,
  unreadMessageCount,
  shouldShowMessageCount,
  isNotificationDropdownOpen,
  setIsNotificationDropdownOpen,
  notifications,
  unreadCount,
  handleMarkAllAsRead,
  handleNotificationClick,
  handleLogout,
  isJobseeker,
  isRecruiter,
  isLoggedIn,
}: DesktopHeaderProps) => {
  const router = useRouter();

  return (
    <div className="container hidden lg:flex h-16 items-center justify-between mx-auto">
      <Link
        href={
          isLoggedIn
            ? isJobseeker
              ? "/dashboard/applied-jobs"
              : "/company-dashboard/all-jobs"
            : "/"
        }
        className="flex items-center space-x-2"
      >
        <Image
          src="/images/logo.png"
          alt="Jobs Logo"
          width={100}
          height={40}
          className="h-10 w-auto"
        />
      </Link>

      <nav className="items-center space-x-6">
        {mainNavLinks.map((link) => (
          <NavLink key={link.href} href={link.href}>
            {link.label}
          </NavLink>
        ))}
      </nav>
      <div className="items-center space-x-4 flex">
        {user ? (
          <>
            {user?.role === UserRole.JOBSEEKER && (
              <>
                {(() => {
                  return isProMember ? (
                    <div className="w-[48px] h-[48px] rounded-full inline-flex justify-center items-center bg-blue-300 text-sm text-blue-100 font-bold">
                      <CrownIcon />
                    </div>
                  ) : (
                    <Button
                      variant="default"
                      className="rounded-full gap-x-2 bg-blue-300 px-3 py-2 text-sm text-blue-100 font-bold"
                      onClick={handlePurchasePro}
                      disabled={isPurchasingPro}
                    >
                      <CrownIcon /> Get Featured
                    </Button>
                  );
                })()}
              </>
            )}

            <Link
              href="/message"
              className="bg-black-100 text-white w-[48px] h-[48px] rounded-full inline-flex justify-center items-center relative"
            >
              {shouldShowMessageCount && unreadMessageCount > 0 && (
                <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs">
                  {unreadMessageCount > 9 ? "9+" : unreadMessageCount}
                </span>
              )}
              <MessageIcon />
            </Link>
            <DropdownMenu
              open={isNotificationDropdownOpen}
              onOpenChange={setIsNotificationDropdownOpen}
            >
              <DropdownMenuTrigger asChild>
                <Button
                  variant="default"
                  className="w-[48px] h-[48px] rounded-full px-0 py-0 relative"
                >
                  {unreadCount > 0 && (
                    <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs">
                      {unreadCount > 9 ? "9+" : unreadCount}
                    </span>
                  )}
                  <BellIconNotification />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80 max-h-96 overflow-y-auto">
                {notifications.length > 0 ? (
                  <>
                    <div className="flex items-center justify-between p-2 border-b">
                      <span className="font-semibold text-sm">Notifications</span>
                      {unreadCount > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleMarkAllAsRead}
                          className="text-xs"
                        >
                          Mark all as read
                        </Button>
                      )}
                    </div>
                    {notifications.map((notification) => (
                      <DropdownMenuItem
                        key={notification._id}
                        className={`p-3 cursor-pointer ${!notification.isRead ? "bg-blue-50" : ""}`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm">{notification.title}</span>
                            {!notification.isRead && (
                              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            )}
                          </div>
                          <span className="text-xs text-gray-600">{notification.message}</span>
                          <span className="text-xs text-gray-600">
                            {new Date(notification.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </DropdownMenuItem>
                    ))}
                  </>
                ) : (
                  <DropdownMenuItem disabled>
                    <span className="text-gray-500">No notifications</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="default" className="w-[48px] h-[48px] rounded-full px-0 py-0">
                  <Image
                    src={user?.profilePicture || DEFAULT_IMAGE}
                    alt="User Profile"
                    width={48}
                    height={48}
                    className="w-[48px] h-[48px] rounded-full object-cover"
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {isJobseeker && (
                  <>
                    <DropdownMenuItem onClick={() => router.push("/dashboard/applied-jobs")}>
                      Dashboard
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => router.push("/settings/my-profile")}>
                      Settings
                    </DropdownMenuItem>
                  </>
                )}
                {isRecruiter && (
                  <>
                    <DropdownMenuItem onClick={() => router.push("/company-dashboard/all-jobs")}>
                      Dashboard
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => router.push("/company-settings/company-profile")}
                    >
                      Company Settings
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem onClick={handleLogout}>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        ) : (
          <>
            <Link href="/login">
              <Button
                variant="outline"
                className="rounded-full border border-blue-100 text-blue-100 px-5"
              >
                Login
              </Button>
            </Link>
            <Link href="/sign-up">
              <Button className="rounded-full px-5 bg-orange-100 hover:bg-orange-100 text-white">
                Sign Up
              </Button>
            </Link>
          </>
        )}
      </div>
    </div>
  );
};

export default DesktopHeader;
