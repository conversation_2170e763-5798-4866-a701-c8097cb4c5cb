"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import ApplyNowButton from "../Buttons/ApplyNowButton";
import {
  CalenderIcon,
  ExperienceIcon,
  LeftIconTop,
  LocationIcon,
  PayIcon,
  QualificationIcon,
  UserRoundIcon,
} from "../Icons";
import SavedRemovedJobBtn from "../SavedRemovedJobBtn";
import JobOverviewCard from "./JobOverviewCard";
import { useUserStore } from "@/store/useUserStore";

interface JobDetailProps {
  jobId: string;
  imageUrl: string;
  jobTitle: string;
  companyName: string;
  category: string;
  jobType: string;
  cityName: string;
  salaryRange: string;
  deadline: string;
  datePosted: string;
  location: string;
  experience: string;
  qualification: string;
  careerLevel: string;
  jobDescription: string;
  keyResponsibilities: string[];
  skillsExperience: string[];
  skillsTags: string[];
  onApplySuccess?: () => void;
  isJobDetailINDashboard?: boolean;
  isApplyNowButton?: boolean;
  isSaved?: boolean;
  premiumExpireAt?: string;
  alreadyApplied?: boolean;
  isSavedButtonShow?: boolean;
  isJobDetailWeb?: boolean;
  isDeadlinePassed?: boolean;
}

const JobDetail: React.FC<JobDetailProps> = ({
  jobId,
  imageUrl,
  jobTitle,
  companyName,
  category,
  jobType,
  cityName,
  salaryRange,
  deadline,
  datePosted,
  location,
  experience,
  qualification,
  careerLevel,
  jobDescription,
  keyResponsibilities,
  skillsExperience,
  skillsTags,
  onApplySuccess,
  isJobDetailINDashboard = false,
  isApplyNowButton = true,
  isSaved = false,
  alreadyApplied = false,
  isJobDetailWeb = false,
  isSavedButtonShow = true,
  premiumExpireAt,
  isDeadlinePassed,
}) => {
  const isPremiumJob = premiumExpireAt ? new Date(premiumExpireAt) > new Date() : false;
  const { currentUser } = useUserStore();
  return (
    <>
      <div className={`rounded-[18px] border bg-offWhite-100 border-gray-200 lg:p-[30px] p-4`}>
        <div className="flex">
          <div className="mr-4">
            <Image
              src={imageUrl}
              alt="Company Logo"
              width={80}
              height={80}
              className="w-[80px] h-[80px] rounded-full"
            />
          </div>
          <div>
            {jobTitle && <h2 className="text-2xl font-medium text-black-100 mb-3">{jobTitle}</h2>}
            {(companyName || category) && (
              <p className="text-black-100">
                {companyName && (
                  <>
                    <span className="text-gray-100">by</span> {companyName}
                  </>
                )}{" "}
                {category && (
                  <>
                    <span className="text-gray-100">in</span> {category}
                  </>
                )}
              </p>
            )}
          </div>
          <div className="flex space-x-4 ml-auto">
            {/* {isPremiumJob && (
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill={isPremiumJob ? "#FF6B00" : "#262626"}
                    d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
                  ></path>
                </svg>
              </span>
            )} */}
            {isPremiumJob && (
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill={isPremiumJob ? "#FF6B00" : "#262626"}
                    d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
                  ></path>
                </svg>
              </span>
            )}
            {currentUser && isSavedButtonShow && !isDeadlinePassed && (
              <span>
                <SavedRemovedJobBtn jobId={jobId} isSaved={isSaved} />
              </span>
            )}
          </div>
        </div>
        <div className="flex gap-4 flex-wrap mt-6">
          {jobType && (
            <div className="bg-white border border-orange-100 text-orange-100 px-6 py-3 rounded-full">
              {jobType}
            </div>
          )}
          {cityName && (
            <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
              {cityName}
            </div>
          )}
          {salaryRange && (
            <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
              {salaryRange}
            </div>
          )}
          {isJobDetailINDashboard && (
            <div className="ml-auto flex items-center gap-4">
              <Link
                href={"#"}
                className="flex items-center gap-x-2 bg-blue-100 text-white px-7 py-3 rounded-full text-lg font-medium"
              >
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill="#fff"
                      d="m11.907 6.918-7 6.875a.94.94 0 0 1-1.314 0l-3-2.946A.939.939 0 0 1 1.907 9.51l2.344 2.302 6.343-6.23a.938.938 0 0 1 1.314 1.337M19.42 5.59a.937.937 0 0 0-1.328-.011L11.75 11.81l-.616-.605a.937.937 0 1 0-1.314 1.337l1.273 1.25a.94.94 0 0 0 1.314 0l7-6.875a.94.94 0 0 0 .012-1.325z"
                    ></path>
                  </svg>
                </span>
                <span>Shortlisted</span>
              </Link>
            </div>
          )}
          {isApplyNowButton && (
            <>
              {isDeadlinePassed ? (
                <div className="ml-auto flex items-center gap-4">
                  <button
                    disabled
                    className={`py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 cursor-not-allowed text-white transition-colors`}
                  >
                    <span className="text">Deadline Passed</span>
                    <span className="icon">
                      <LeftIconTop />
                    </span>
                  </button>
                </div>
              ) : (
                <>
                  <div className="ml-auto flex items-center gap-4">
                    {alreadyApplied ? (
                      <>
                        <button
                          disabled
                          className={`py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 cursor-not-allowed text-white transition-colors`}
                        >
                          <span className="text">Already Applied</span>
                          <span className="icon">
                            <LeftIconTop />
                          </span>
                        </button>
                      </>
                    ) : (
                      <>
                        <ApplyNowButton jobId={jobId} onSuccess={onApplySuccess} />
                      </>
                    )}
                  </div>
                </>
              )}
            </>
          )}
        </div>
        {deadline && (
          <div className="flex justify-between mt-6">
            <div className="text-orange-100">
              Deadline Date: <span className="text-black-100">{deadline}</span>{" "}
            </div>
          </div>
        )}
      </div>
      <div className={`${isJobDetailWeb ? "h-[calc(100vh-370px)] overflow-y-auto" : ""}`}>
        {(datePosted || location || salaryRange || experience || qualification || careerLevel) && (
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100">Job Overview</h2>
            <div className="grid xl:grid-cols-3 grid-cols-2 gap-6 mt-5 pb-6">
              {datePosted && (
                <JobOverviewCard
                  icon={<CalenderIcon />}
                  title="Date Posted"
                  titleAccording={datePosted}
                />
              )}
              {location && (
                <JobOverviewCard
                  icon={<LocationIcon />}
                  title="Location"
                  titleAccording={location}
                />
              )}
              {salaryRange && (
                <JobOverviewCard
                  icon={<PayIcon />}
                  title="Offered Salary"
                  titleAccording={salaryRange}
                />
              )}

              {experience && (
                <JobOverviewCard
                  icon={<ExperienceIcon />}
                  title="Experience"
                  titleAccording={experience}
                />
              )}

              {qualification && (
                <JobOverviewCard
                  icon={<QualificationIcon />}
                  title="Qualification"
                  titleAccording={qualification}
                />
              )}

              {careerLevel && (
                <JobOverviewCard
                  icon={<UserRoundIcon />}
                  title="Career Level"
                  titleAccording={careerLevel}
                />
              )}
            </div>
            <hr />
          </div>
        )}
        {jobDescription && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Job Description</h2>
            <p className="text-base font-normal text-gray-100 leading-6">{jobDescription}</p>
          </div>
        )}
        {keyResponsibilities && keyResponsibilities.length > 0 && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Key Responsibilities</h2>
            <ul>
              {keyResponsibilities.map((responsibility, index) => (
                <li
                  key={index}
                  className="text-base font-normal text-gray-100 mb-3 flex items-center gap-x-2"
                >
                  <span className="w-[5px] h-[5px] bg-gray-100 rounded-full flex justify-center items-center"></span>{" "}
                  {responsibility}
                </li>
              ))}
            </ul>
          </div>
        )}
        {skillsExperience && skillsExperience.length > 0 && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Skills & Experience</h2>
            <ul>
              {skillsExperience.map((item, index) => (
                <li
                  key={index}
                  className="text-base font-normal text-gray-100 mb-3 flex items-center gap-x-2"
                >
                  <span className="w-[5px] h-[5px] bg-gray-100 rounded-full flex justify-center items-center"></span>{" "}
                  {item}
                </li>
              ))}
            </ul>
          </div>
        )}
        {skillsTags && skillsTags.length > 0 && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Skills Tags</h2>
            <ul className="flex flex-wrap gap-4">
              {skillsTags.map((item, index) => (
                <li
                  key={index}
                  className="border border-gray-300 inline-block text-gray-100 text-base font-normal px-6 py-3 rounded-full"
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </>
  );
};

export default JobDetail;
